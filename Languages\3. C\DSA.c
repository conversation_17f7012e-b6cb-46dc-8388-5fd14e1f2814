#include <stdio.h>
#include <stdlib.h>


struct node{
    int data;
    struct node *link;
};

struct node *head = NULL;




struct node* CreateNode(int value){
    struct node *newnode = (struct node*)malloc(sizeof(struct node));
    newnode -> data = value;
    newnode -> link = NULL;

    return newnode;
}





void InsertFirst(int value){
    struct node *temp = CreateNode(value); 
    temp-> link = head;
    head = temp;  

}




void Insertlast(int value){
    if (head == NULL){
        head = CreateNode(value);
        return;
    }

    else{
    struct node *temp = head;
    while(temp->link != NULL){
        temp = temp -> link;
    }

    temp -> link = CreateNode(value);
    }

}





void InsertAfter(int value, int target){
    

    struct node *temp = head;
    while(temp != NULL && temp -> data != target){
        temp = temp -> link;
    }

    if (temp == NULL){
        printf("Target %d not found in the list\n", target);
        return;
    }
    
    
    if (temp == NULL && temp -> data != target){
        printf("Target %d not found in the list\n", target);
        return;
    }

    struct node *newnode = CreateNode(value);
    newnode -> link = temp -> link;
    temp -> link = newnode;
}





// void InsertBefore(int value, int target){
//     if (head == NULL){
//         printf("List is empty\n");
//         return;
//     }

//     struct node *temp = head;

//     if (temp -> data != target){
//         temp = temp -> link;
//     }

//     struct node *newnode =CreateNode(value);
//     newnode -> link = temp -> link;
//     temp -> link = newnode;

// }

void InsertBefore(int value, int target){
    if (head == NULL){
        printf("List is empty\n");
        return;
    }

    
    if (head->data == target){
        struct node *newnode = CreateNode(value);
        newnode->link = head;
        head = newnode;
        return;
    }

    struct node *temp = head;
    while(temp->link != NULL && temp->link->data != target){
        temp = temp->link;
    }

    if (temp->link == NULL){
        printf("Target %d not found in the list\n", target);
        return;
    }

    struct node *newnode = CreateNode(value);
    newnode->link = temp->link;  
    temp->link = newnode;        
}






void Delete(int target){
    struct node *temp = head;

    if (head == NULL) {
        printf("List is empty\n");
        return;
    }

    if(temp -> data == target){
        head = temp -> link;
        free(temp);
        return;
    }

    while(temp != NULL && temp -> link -> data != target){
        temp  = temp -> link;

        if (temp -> link == NULL ){
        printf("Target %d not found in the list\n", target);
        return;
    }
    }


    struct node *deletenode;
    deletenode = temp -> link;
    temp -> link = deletenode -> link;
    free(deletenode);
}






void Display(){
    struct node *temp = head;
    while(temp != NULL){
        printf("%d -> ",temp -> data);
        temp = temp -> link;
    }
    printf("NULL");
}





int main(){
    
    while(1){

    int Choise, data , target;
    printf("Pick any option: \n 1)Insert First \n 2)Insert Last \n 3)Insert After \n 4)Insert Before \n 5)Delete \n 6)Display \n 7)Exit \n");
    scanf("%d",&Choise);

    if(Choise < 1 || Choise > 8){
        printf("Invalid Choise\n");
    }
    
    else{
        switch(Choise){
            case 1: {
                printf("Enter any number to insert first: ");
                scanf("%d",&data);

                InsertFirst(data);
                break;
            }

            case 2: {
                printf("Enter any number to insert last: ");
                scanf("%d",&data);

                Insertlast(data);
                break;
            }

            case 3: {
                printf("Enter any number to insert after: ");
                scanf("%d",&data);

                printf("Enter target: ");
                scanf("%d",&target);

                InsertAfter(data,target);
                break;
            }

            case 4: {
                printf("Enter any number to insert before: ");
                scanf("%d",&data);

                printf("Enter target: ");
                scanf("%d",&target);

                InsertBefore(data,target);
                break;
            }

            case 5: {
                printf("Enter target: ");
                scanf("%d",&target);

                Delete(target);
                break;
            }

            case 6: {
                Display();
                break;
            }

            case 7: {
                printf("Exiting...");
                return 0;
            }
        }
    }
    }


    return 0;
}