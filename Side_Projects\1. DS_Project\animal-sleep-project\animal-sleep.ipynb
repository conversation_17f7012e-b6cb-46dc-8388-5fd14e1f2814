{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Animal Sleep\n", "\n", "Some animals, such as elephants, sleep only 3-4 hours a night, while others, like bats, can sleep up to 20 hours.\n", "\n", "Is there a relationship between sleep duration and body mass? Is a species' diet correlated with sleep time?\n", "\n", "The file `animal-sleep.csv` provides data on the average sleep duration of 77 animal species, along with their average body mass in kilograms."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# FOR <PERSON>O<PERSON>LE COLAB ONLY.\n", "# Uncomment and run the code below. A dialog will appear to upload files.\n", "# Upload 'animal-sleep.csv' and 'linear_model.py'.\n", "\n", "# from google.colab import files\n", "# uploaded = files.upload()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>animal</th>\n", "      <th>diet</th>\n", "      <th>sleep</th>\n", "      <th>mass</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>african elephant</td>\n", "      <td>herbivore</td>\n", "      <td>3.3</td>\n", "      <td>6654.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>african giant pouched rat</td>\n", "      <td>omnivore</td>\n", "      <td>8.3</td>\n", "      <td>1.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>african striped mouse</td>\n", "      <td>herbivore</td>\n", "      <td>8.7</td>\n", "      <td>0.044</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>arctic fox</td>\n", "      <td>carnivore</td>\n", "      <td>12.5</td>\n", "      <td>3.380</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>arctic ground squirrel</td>\n", "      <td>omnivore</td>\n", "      <td>16.6</td>\n", "      <td>0.920</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      animal       diet  sleep      mass\n", "0           african elephant  herbivore    3.3  6654.000\n", "1  african giant pouched rat   omnivore    8.3     1.000\n", "2      african striped mouse  herbivore    8.7     0.044\n", "3                 arctic fox  carnivore   12.5     3.380\n", "4     arctic ground squirrel   omnivore   16.6     0.920"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from linear_model import LinearModel\n", "\n", "df = pd.read_csv('animal-sleep.csv')\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'hours of sleep per day')"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 400x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(4, 3))\n", "plt.scatter(df['mass'], df['sleep'])\n", "plt.xlabel('Mass (kg)')\n", "plt.ylabel('hours of sleep per day')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We see that the mass data exhibits a strong right skew with a long tail. A log transformation can convert an exponential relationship into a linear one, making it easier to model with linear regression."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>animal</th>\n", "      <th>diet</th>\n", "      <th>sleep</th>\n", "      <th>mass</th>\n", "      <th>log10_mass</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>african elephant</td>\n", "      <td>herbivore</td>\n", "      <td>3.3</td>\n", "      <td>6654.000</td>\n", "      <td>3.823083</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>african giant pouched rat</td>\n", "      <td>omnivore</td>\n", "      <td>8.3</td>\n", "      <td>1.000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>african striped mouse</td>\n", "      <td>herbivore</td>\n", "      <td>8.7</td>\n", "      <td>0.044</td>\n", "      <td>-1.356547</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>arctic fox</td>\n", "      <td>carnivore</td>\n", "      <td>12.5</td>\n", "      <td>3.380</td>\n", "      <td>0.528917</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>arctic ground squirrel</td>\n", "      <td>omnivore</td>\n", "      <td>16.6</td>\n", "      <td>0.920</td>\n", "      <td>-0.036212</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      animal       diet  sleep      mass  log10_mass\n", "0           african elephant  herbivore    3.3  6654.000    3.823083\n", "1  african giant pouched rat   omnivore    8.3     1.000    0.000000\n", "2      african striped mouse  herbivore    8.7     0.044   -1.356547\n", "3                 arctic fox  carnivore   12.5     3.380    0.528917\n", "4     arctic ground squirrel   omnivore   16.6     0.920   -0.036212"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import math\n", "df['log10_mass'] = df['mass'].apply(math.log10)  # apply the function to each element\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Below is code for plotting the data using the log scale."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(6, 4))\n", "plt.scatter(df['log10_mass'], df['sleep'])\n", "plt.xlabel('Mass (kg)')\n", "plt.ylabel('hours of sleep per day')\n", "xticks = [-2, -1, 0, 1, 2, 3, 4]\n", "xtick_labels = [0.01, 0.1, 1, 10, 100, 1000, 10000]\n", "plt.xticks(xticks, xtick_labels)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Project Ideas\n", "\n", "- Model the relationship between `log10_mass` and hours of `sleep`.\n", "\n", "- Create three separate linear models for each `diet` category: carnivores, omnivores, and herbivores.\n", "\n", "- Visualize the data and their best-fit lines on a single plot with distinct colors and symbols for each `diet` category, or use separate plots if more suitable.\n", "\n", "- Explore and propose mechanisms that could explain the results, potentially involving additional research."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# YOUR CODE HERE (add additional cells as needed)\n", "\n", "# Linear model API\n", "\n", "# linear = LinearModel()\n", "# linear.fit(x, y)\n", "# linear.predict(x)\n", "# linear.plot_model(x_min, x_max, color=\"black\")\n", "# linear.print_model_info(self):\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}