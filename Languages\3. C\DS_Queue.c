# include <stdio.h>

int queue[4];
int front = -1;
int rear = -1;
int size = 4;




void enqueue(int data){
    if(rear == size-1){
        printf("Queue Overflow\n");
    }

    else {

        if(front == -1){
            front = rear = 0;
            queue[rear] = data;
        }

        else{
            rear++;
            queue[rear] = data;
        }

    printf("Enqueued %d\n",data);    
    }
}




void dequeue(){
    if(front == -1 || front > rear){
        printf("Queue Underflow\n");
    }

    else{
        printf("Dequeued %d\n",queue[front]);
        front++;
    }
}




void display(){
    for(int i = front ;i <= rear; i++){
        printf("%d <- ",queue[i]);
    }
}




// int main(){
//     enqueue(10);
//     enqueue(20);
//     enqueue(30);
//     enqueue(40);
//     dequeue();
//     display();

//     return 0;
// }


int main(){
    
    while(1){

    int Choise, data;
    printf("Pick any option: \n 1)Enqueue \n 2)Dequeue \n 3)Display \n 4)Exit \n");
    scanf("%d",&<PERSON><PERSON>);

    if(Choise < 1 || Choi<PERSON> > 5){
        printf("Invalid Choise\n");
    }
    
    else{
        switch(Choise){
            case 1: {
                printf("Enter any number to push: ");
                scanf("%d",&data);

                enqueue(data);
                break;
            }

            case 2: {
                dequeue();
                break;
            }

            case 3: {
                display();
                break;
            }

            case 4: {
                printf("Exiting...");
                return 0;
            }
        }
    }
    }


    return 0;
}