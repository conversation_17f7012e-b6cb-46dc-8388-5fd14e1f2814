# FOR GOOGLE COLAB ONLY.
# Uncomment and run the code below. A dialog will appear to upload files.
# Upload 'largest-islands.csv'.

# from google.colab import files
# uploaded = files.upload()

import pandas as pd
import matplotlib.pyplot as plt

df = pd.read_csv('largest-islands.csv')
df.head(10)

# What are the 10 largest islands in the tropics?

df_largest_island = df.sort_values(by='rank', ascending=True)
df_largest_island.head(10)

# What are the largest islands in each `region`?

df_largestperregion = df.groupby('region').max('area')
df_largestperregion.head(10)


# Create a line graph with `area` on the y-axis and `rank` on the x-axis.The data should be ordered by `rank`,from largest to smallest

x = df.plot(x='rank', y='area', ascending=False)
plt.show()