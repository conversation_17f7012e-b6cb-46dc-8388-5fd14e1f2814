import java.util.Scanner;

class Student {
    String name;
    int rollNo;

    Student(String name, int rollNo) {
        this.name = name;
        this.rollNo = rollNo;
    }
}

class Test extends Student{
    int subject1, subject2;
    Test(String name, int rollNo, int subject1, int subject2) {
        super(name, rollNo);
        this.subject1 = subject1;
        this.subject2 = subject2;
    }
}

interface Sports {
    int marks = 5;
}

class Result extends Test implements Sports{
    int finalMarks;

    Result(String name, int rollNo, int subject1, int subject2) {
        super(name, rollNo, subject1, subject2);
    }

    public void getResult() {
        finalMarks = this.subject1 + this.subject2 + marks;
        System.out.printf("%s (Roll No: %d) has scored %d marks in total.\n", this.name,this.rollNo, this.finalMarks);
    }
}

public class Exp4 {
    public static void main(String[] args) {
        String name;    
        int rollNo, subject1, subject2;
        Scanner scanner = new Scanner(System.in);

        System.out.print("Enter the Student's Name: ");
        name = scanner.nextLine();
        System.out.print("Enter the Student's Roll No: ");
        rollNo = scanner.nextInt();
        System.out.print("Enter the Student's Subject 1 marks: ");
        subject1 = scanner.nextInt();
        System.out.print("Enter the Student's Subject 2 marks: ");
        subject2 = scanner.nextInt();

        Result result = new Result(name, rollNo, subject1, subject2);
        result.getResult();

        scanner.close();
    }
}