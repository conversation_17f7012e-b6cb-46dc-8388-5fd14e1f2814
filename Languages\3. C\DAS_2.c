/**
Q) Implement Binary Search tree for the following operations
1)Insert
2)Delete
3)Search
4)Travarsals
5)Count

**/

#include <stdio.h>
#include <stdlib.h>



struct node {
    int data;
    struct node *left;
    struct node *right;
};

struct node* root = NULL;



struct node* createNode(int element) {
    struct node* newNode = (struct node*)malloc(sizeof(struct node));
    newNode->data = element;
    newNode->left = NULL;
    newNode->right = NULL;
    return newNode;
}

void insert(int element){
   
    struct node* current = root;
    struct node* follow = NULL;
    struct node* newNode = createNode(element);
   
    if (root == NULL){
        root = newNode;
        printf("Inserted: %d\n",element);
        return;
    }
   
     while (current != NULL) {
        follow = current;
        if (element >= current->data) {
            current = current->right;
        } else {
            current = current->left;
        }
       
    }
    printf("Inserted: %d\n",element);
   
    if (element >= follow->data) {
        follow->right = newNode;
    }
   
    else {
        follow->left = newNode;
    }
   
}

void search(int element) {
    int level = 0;  
    struct node* current = root;
   

    while (current != NULL && current->data != element) {
        if (element > current->data) {
            current = current->right;
        } else {
            current = current->left;
        }
        level++;
    }
   
    if (current != NULL && current->data == element) {
        printf("The node %d is found at level: %d\n", element, level);
    } else {
        printf("Element %d not found in the tree.\n", element);
    }
}

int main() {
    insert(50);
    insert(30);
    insert(20);
    insert(40);
   
    search(20);


    return 0;
}












// struct node* delete(struct node* root, int element) {
//     // Base case: If the tree is empty
//     if (root == NULL) return root;

//     // Recur down the tree
//     if (element < root->data) {
//         root->left = delete(root->left, element);
//     } else if (element > root->data) {
//         root->right = delete(root->right, element);
//     } else {
//         // Node with only one child or no child
//         if (root->left == NULL) {
//             struct node* temp = root->right;
//             free(root);
//             return temp;
//         } else if (root->right == NULL) {
//             struct node* temp = root->left;
//             free(root);
//             return temp;
//         }

//         // Node with two children, get the inorder successor (smallest in the right subtree)
//         struct node* temp = root->right;
//         while (temp && temp->left != NULL) {
//             temp = temp->left;
//         }

//         // Copy the inorder successor's content to this node
//         root->data = temp->data;

//         // Delete the inorder successor
//         root->right = delete(root->right, temp->data);
//     }
//     return root;
// }
