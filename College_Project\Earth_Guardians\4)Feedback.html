<!DOCTYPE html>
<html lang="en">
<head>
    <title>Feedback</title>
    <link rel="stylesheet" href="4A)Feedback.css">
    <script src="4B)Feedback.js"></script> 
</head>
<style>
  body {
  font-family: 'Arial', sans-serif;
  margin: 0;
  padding: 0;
  background-color: rgba(233, 255, 228, 0.515);
  opacity: 88%;
  color: #333;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}








.form_box {
  background: #ffffff;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  text-align: center;
  max-width: 500px;
  width: 90%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.form_box h2 {
  font-size: 28px;
  color: #004d40;
  margin-bottom: 10px;
}

.form_box p {
  font-size: 16px;
  color: #555;
  margin-bottom: 20px;
}







form {
  display: flex;
  flex-direction: column;
}

input, textarea {
  width: 100%;
  padding: 10px 15px;
  margin: 10px 0;
  border: 1px solid #ccc;
  border-radius: 5px;
  font-size: 16px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input:focus, textarea:focus {
  border-color: #00796b;
  box-shadow: 0 0 5px rgba(0, 121, 107, 0.5);
  outline: none;
}

textarea {
  min-height: 120px;
  resize: none;
}



button {
  background: #008631;
  color: white;
  padding: 12px 20px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

button:hover {
  background: #00796b;
  transform: translateY(-3px);
  opacity: 80%;
}

button:active {
  transform: translateY(0);
}







#form-response {
  font-size: 16px;
  color: #00796b;
  margin-top: 20px;
}

.hidden {
  display: none;
}




</style>

<body>
    <section class="form_box" id="contact">

    <h2>Feedback</h2>
    <p>Let us know your Feedback ,feel free to reach out!</p>
    <br>



    <form id="contact-form">

      <input type="text" id="name" placeholder="Your Name" required>
      <br>
      <br>
      <input type="email" id="email" placeholder="Your Email" required>
      <br>
      <br>
      <textarea id="message" placeholder="Your Message" required></textarea>
      <br>
      <br>
      <button type="submit">Send Message</button>
      
    </form>
        


    <p id="form-response" class="hidden"></p>


  </section>
</body>
</html>