{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1dc27f07", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original data shape: (1087, 4)\n", "\n", "First 10 rows:\n", "          timestamp  heart_rate  dive_id dive_phase\n", "0  8/27/18 17:10:10         NaN        2    descent\n", "1  8/27/18 17:10:22        5.24        2    descent\n", "2  8/27/18 17:10:31        6.55        2    descent\n", "3  8/27/18 17:10:40         NaN        2    descent\n", "4  8/27/18 17:10:48         NaN        2    descent\n", "5  8/27/18 17:10:56         NaN        2    descent\n", "6  8/27/18 17:11:06         NaN        2    descent\n", "7  8/27/18 17:11:17         NaN        2    descent\n", "8  8/27/18 17:11:20         NaN        2    descent\n", "9  8/27/18 17:11:23         NaN        2    descent\n", "\n", "==================================================\n", "WARMUP: Average Heart Rates by Dive Phase\n", "==================================================\n", "dive_phase\n", "ascent     20.433318\n", "descent     7.473607\n", "filter      6.561053\n", "lunge       9.773529\n", "surface    32.546041\n", "Name: heart_rate, dtype: float64\n", "\n", "==================================================\n", "CHALLENGE: Dive Duration vs Max Surface Heart Rate\n", "==================================================\n", "1. Converting timestamp to datetime...\n", "✓ Timestamp conversion complete\n", "\n", "2. Calculating dive duration...\n", "✓ Dive duration calculated for 8 dives\n", "Dive durations (minutes):\n", "   dive_id  dive_duration\n", "0        2      12.433333\n", "1        3      12.400000\n", "2        4       9.000000\n", "3        5       9.616667\n", "4        7      11.216667\n", "5       10      11.016667\n", "6       11      16.400000\n", "7       13      10.916667\n", "\n", "3. Determining maximum surface heart rate...\n", "✓ Max surface heart rate calculated for 8 dives\n", "Max surface heart rates:\n", "   dive_id  max_surface_heart_rate\n", "0        2                   35.50\n", "1        3                   35.29\n", "2        4                   34.48\n", "3        5                   33.90\n", "4        7                   33.52\n", "5       10                   35.29\n", "6       11                   38.22\n", "7       13                   34.88\n", "\n", "4. Merging dataframes...\n", "✓ Final merged dataframe with 8 dives\n", "Final analysis dataset:\n", "   dive_id  dive_duration  max_surface_heart_rate\n", "0        2      12.433333                   35.50\n", "1        3      12.400000                   35.29\n", "2        4       9.000000                   34.48\n", "3        5       9.616667                   33.90\n", "4        7      11.216667                   33.52\n", "5       10      11.016667                   35.29\n", "6       11      16.400000                   38.22\n", "7       13      10.916667                   34.88\n", "\n", "5. Creating visualization...\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "==================================================\n", "ADDITIONAL ANALYSIS\n", "==================================================\n", "Correlation between dive duration and max surface heart rate: 0.884\n", "\n", "Dive Duration Summary:\n", "  Mean: 11.62 minutes\n", "  Median: 11.12 minutes\n", "  Range: 9.00 - 16.40 minutes\n", "\n", "Max Surface Heart Rate Summary:\n", "  Mean: 35.13 BPM\n", "  Median: 35.09 BPM\n", "  Range: 33.52 - 38.22 BPM\n", "\n", "Unique dive phases in dataset: ['descent' 'lunge' 'filter' 'ascent' 'surface']\n", "Total dives analyzed: 8\n", "\n", "✓ Analysis complete!\n"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "# Load the data\n", "df = pd.read_csv('blue-whale-heart-rates.csv')\n", "\n", "print(\"Original data shape:\", df.shape)\n", "print(\"\\nFirst 10 rows:\")\n", "print(df.head(10))\n", "\n", "# ===== WARMUP: Calculate average heart rates for each dive phase =====\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"WARMUP: Average Heart Rates by Dive Phase\")\n", "print(\"=\"*50)\n", "\n", "avg_heart_rates = df.groupby('dive_phase')['heart_rate'].mean()\n", "print(avg_heart_rates)\n", "\n", "# ===== CHALLENGE: Investigate relationship between dive duration and max surface heart rate =====\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"CHALLENGE: Dive Duration vs Max Surface Heart Rate\")\n", "print(\"=\"*50)\n", "\n", "# 1. Data Preparation: Convert timestamp to datetime\n", "print(\"1. Converting timestamp to datetime...\")\n", "df['timestamp'] = pd.to_datetime(df['timestamp'], format='%m/%d/%y %H:%M:%S')\n", "print(\"✓ Timestamp conversion complete\")\n", "\n", "# 2. Calculate Dive Duration\n", "print(\"\\n2. Calculating dive duration...\")\n", "\n", "# Get earliest timestamp for descent phase for each dive_id\n", "descent_start = df[df['dive_phase'] == 'descent'].groupby('dive_id')['timestamp'].min().reset_index()\n", "descent_start.columns = ['dive_id', 'descent_start']\n", "\n", "# Get latest timestamp for ascent phase for each dive_id  \n", "ascent_end = df[df['dive_phase'] == 'ascent'].groupby('dive_id')['timestamp'].max().reset_index()\n", "ascent_end.columns = ['dive_id', 'ascent_end']\n", "\n", "# Merge to calculate dive duration\n", "dive_times = pd.merge(descent_start, ascent_end, on='dive_id')\n", "dive_times['dive_duration'] = (dive_times['ascent_end'] - dive_times['descent_start']).dt.total_seconds() / 60\n", "\n", "# Create dive duration dataframe\n", "dive_duration_df = dive_times[['dive_id', 'dive_duration']]\n", "print(f\"✓ Dive duration calculated for {len(dive_duration_df)} dives\")\n", "print(\"Dive durations (minutes):\")\n", "print(dive_duration_df)\n", "\n", "# 3. Determine Maximum Surface Heart Rate\n", "print(\"\\n3. Determining maximum surface heart rate...\")\n", "\n", "# Get maximum heart rate for surface phase for each dive_id\n", "max_surface_hr = df[df['dive_phase'] == 'surface'].groupby('dive_id')['heart_rate'].max().reset_index()\n", "max_surface_hr.columns = ['dive_id', 'max_surface_heart_rate']\n", "\n", "print(f\"✓ Max surface heart rate calculated for {len(max_surface_hr)} dives\")\n", "print(\"Max surface heart rates:\")\n", "print(max_surface_hr)\n", "\n", "# 4. <PERSON><PERSON>\n", "print(\"\\n4. Merging dataframes...\")\n", "final_df = pd.merge(dive_duration_df, max_surface_hr, on='dive_id')\n", "print(f\"✓ Final merged dataframe with {len(final_df)} dives\")\n", "print(\"Final analysis dataset:\")\n", "print(final_df)\n", "\n", "# 5. Visualize the Data\n", "print(\"\\n5. Creating visualization...\")\n", "\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(final_df['dive_duration'], final_df['max_surface_heart_rate'], \n", "           alpha=0.7, s=100, color='steelblue', edgecolors='black', linewidth=0.5)\n", "\n", "plt.xlabel('Dive Duration (minutes)', fontsize=12)\n", "plt.ylabel('Maximum Surface Heart Rate (BPM)', fontsize=12)\n", "plt.title('Relationship between Dive Duration and Maximum Surface Heart Rate\\nin Blue Whales', \n", "          fontsize=14, fontweight='bold')\n", "\n", "# Add trend line\n", "z = np.polyfit(final_df['dive_duration'], final_df['max_surface_heart_rate'], 1)\n", "p = np.poly1d(z)\n", "plt.plot(final_df['dive_duration'], p(final_df['dive_duration']), \n", "         \"r--\", alpha=0.8, linewidth=2, label=f'Trend line (slope: {z[0]:.2f})')\n", "\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Additional analysis\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"ADDITIONAL ANALYSIS\")\n", "print(\"=\"*50)\n", "\n", "# Calculate correlation\n", "correlation = final_df['dive_duration'].corr(final_df['max_surface_heart_rate'])\n", "print(f\"Correlation between dive duration and max surface heart rate: {correlation:.3f}\")\n", "\n", "# Summary statistics\n", "print(f\"\\nDive Duration Summary:\")\n", "print(f\"  Mean: {final_df['dive_duration'].mean():.2f} minutes\")\n", "print(f\"  Median: {final_df['dive_duration'].median():.2f} minutes\")\n", "print(f\"  Range: {final_df['dive_duration'].min():.2f} - {final_df['dive_duration'].max():.2f} minutes\")\n", "\n", "print(f\"\\nMax Surface Heart Rate Summary:\")\n", "print(f\"  Mean: {final_df['max_surface_heart_rate'].mean():.2f} BPM\")\n", "print(f\"  Median: {final_df['max_surface_heart_rate'].median():.2f} BPM\")\n", "print(f\"  Range: {final_df['max_surface_heart_rate'].min():.2f} - {final_df['max_surface_heart_rate'].max():.2f} BPM\")\n", "\n", "# Check unique dive phases in the data\n", "print(f\"\\nUnique dive phases in dataset: {df['dive_phase'].unique()}\")\n", "print(f\"Total dives analyzed: {df['dive_id'].nunique()}\")\n", "\n", "print(\"\\n✓ Analysis complete!\")"]}, {"cell_type": "markdown", "id": "bcaa22e4", "metadata": {}, "source": ["Got it — you’ve uploaded a Jupyter Notebook (`whale-heart-rates.ipynb`). To help you solve the challenges, I’ll first need to open and inspect the contents (the questions, code cells, etc.).\n", "\n", "Let me extract and review what’s inside the notebook so I can create new snippets for each challenge.\n", "\n", "\n", "I see the structure now 👍\n", "\n", "The notebook has:\n", "\n", "* An intro about **blue whale heart rates**.\n", "* A **warmup task**: *Calculate average heart rates for each dive phase*.\n", "* A **challenge task**: *Investigate relationship between dive duration and max surface heart rate*.\n", "* Some partially attempted code cells (but not correct/complete).\n", "\n", "You asked me to **solve the challenges and make new snippets for each question**.\n", "Here’s what I’ll do:\n", "\n", "### New snippets for each challenge\n", "\n", "---\n", "\n", "#### **Warmup** – Average heart rates per dive phase\n", "\n", "```python\n", "# Warmup: Calculate the average heart rate for each dive phase\n", "avg_hr_per_phase = df.groupby(\"dive_phase\")[\"heart_rate\"].mean().reset_index()\n", "print(avg_hr_per_phase)\n", "```\n", "\n", "---\n", "\n", "#### **Challenge Part 1** – Calculate dive durations\n", "\n", "```python\n", "# 1. Find earliest descent time and latest ascent time per dive\n", "dive_start = df[df[\"dive_phase\"] == \"descent\"].groupby(\"dive_id\")[\"timestamp\"].min()\n", "dive_end = df[df[\"dive_phase\"] == \"ascent\"].groupby(\"dive_id\")[\"timestamp\"].max()\n", "\n", "# 2. Compute dive duration in minutes\n", "dive_duration = (dive_end - dive_start).dt.total_seconds() / 60\n", "dive_duration_df = dive_duration.reset_index().rename(columns={0: \"dive_duration\"})\n", "print(dive_duration_df.head())\n", "```\n", "\n", "---\n", "\n", "#### **Challenge Part 2** – Maximum surface heart rate after dives\n", "\n", "```python\n", "# Get the maximum heart rate during surface phase for each dive\n", "max_surface_hr = df[df[\"dive_phase\"] == \"surface\"].groupby(\"dive_id\")[\"heart_rate\"].max()\n", "max_surface_hr_df = max_surface_hr.reset_index().rename(columns={\"heart_rate\": \"max_surface_heart_rate\"})\n", "print(max_surface_hr_df.head())\n", "```\n", "\n", "---\n", "\n", "#### **Challenge Part 3** – Merge and analyze\n", "\n", "```python\n", "# Merge dive duration and max surface heart rate\n", "merged = pd.merge(dive_duration_df, max_surface_hr_df, on=\"dive_id\")\n", "print(merged.head())\n", "```\n", "\n", "---\n", "\n", "#### **Challenge Part 4** – <PERSON><PERSON>er plot\n", "\n", "```python\n", "# Scatter plot of dive duration vs. max surface heart rate\n", "plt.figure(figsize=(8,6))\n", "plt.scatter(merged[\"dive_duration\"], merged[\"max_surface_heart_rate\"], color=\"blue\", alpha=0.7)\n", "plt.xlabel(\"Dive Duration (minutes)\")\n", "plt.ylabel(\"Max Surface Heart Rate (bpm)\")\n", "plt.title(\"Relationship Between Dive Duration and Max Surface Heart Rate\")\n", "plt.grid(True, linestyle=\"--\", alpha=0.5)\n", "plt.show()\n", "```\n", "\n", "---\n", "\n", "Do you want me to **run these snippets on your dataset** right now so you can directly see the answers (tables + graph), or just keep them as code solutions?\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}