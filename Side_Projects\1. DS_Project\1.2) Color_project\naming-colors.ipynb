{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Naming colors\n", "\n", "If you are shown random colors evenly spaced across the color spectrum, what percentage of those colors would the average person name red? What about green? \n", "\n", "English speakers in the United States were shown 80 color chips in random order in controlled lighting condtions. These 80 colors are evenly spaced across the standard <PERSON><PERSON>ll array of colors. The participants were told: \"There are 11 choices: black, white, red, green, blue, purple, brown, yellow, orange, pink, gray. Choose the closest color word\".\n", "\n", "This study was then repeated with Bolivian-Spanish speakers in Bolivia and Tsimane' speakers from the Amazon.\n", "\n", "The dataset `munsell-array-fixed-choice.csv` shows the most common name in each language for each color chip."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# FOR <PERSON>O<PERSON>LE COLAB ONLY.\n", "# Uncomment and run the code below. A dialog will appear to upload files.\n", "# Upload 'munsell-array-fixed-choice.csv'.\n", "\n", "# from google.colab import files\n", "# uploaded = files.upload()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The function below plots the 80 evenly spaced colors that each study participant was shown in random order."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_colors(color_column):\n", "    fig, ax = plt.subplots()\n", "    for idx, row in df.iterrows():\n", "        ax.add_patch(plt.Rectangle((row['x'] - 0.5, row['y'] - 0.5), 1, 1, color=row[color_column], linewidth=0))\n", "\n", "    ax.set_xlim(0.5, 20.5)\n", "    ax.set_ylim(0.5, 8.5)\n", "    ax.set_aspect('equal')\n", "    ax.set_facecolor('black')\n", "    ax.set_xticks(range(1, 21))\n", "    ax.set_yticks(range(1, 9), ['H', 'G', 'F', 'E', 'D', 'C', 'B', 'A'])\n", "\n", "plot_colors('tile_hex')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also use this function to plot the most common name in each language for each color chip."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Also try 'spanish_color' and 'tsimane_color'\n", "plot_colors('english_color')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>grid</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>munsell_code</th>\n", "      <th>tile_hex</th>\n", "      <th>english_color</th>\n", "      <th>spanish_color</th>\n", "      <th>tsimane_color</th>\n", "      <th>spanish</th>\n", "      <th>tsimane</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>B1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>5R8/6</td>\n", "      <td>#fbb6b0</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>yellow</td>\n", "      <td>rosada (pink)</td>\n", "      <td>chamus (yellow)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>5R6/12</td>\n", "      <td>#eb6a68</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>F1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>5R4/14</td>\n", "      <td>#bb1933</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>rojo (red)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>H1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5R2/8</td>\n", "      <td>#610d25</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>brown</td>\n", "      <td>rojo (red)</td>\n", "      <td>cafedyeisi (brown)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A2</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>10R9/2</td>\n", "      <td>#f2ded8</td>\n", "      <td>pink</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>C2</td>\n", "      <td>2</td>\n", "      <td>6</td>\n", "      <td>10R7/10</td>\n", "      <td>#fb916b</td>\n", "      <td>orange</td>\n", "      <td>orange</td>\n", "      <td>yellow</td>\n", "      <td>naranja (orange)</td>\n", "      <td>chamus (yellow)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>E2</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>10R5/16</td>\n", "      <td>#db4300</td>\n", "      <td>orange</td>\n", "      <td>orange</td>\n", "      <td>red</td>\n", "      <td>naranja (orange)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>G2</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>10R3/10</td>\n", "      <td>#872110</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>brown</td>\n", "      <td>rojo (red)</td>\n", "      <td>cafedyeisi (brown)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>B3</td>\n", "      <td>3</td>\n", "      <td>7</td>\n", "      <td>5YR8/8</td>\n", "      <td>#ffb779</td>\n", "      <td>orange</td>\n", "      <td>pink</td>\n", "      <td>yellow</td>\n", "      <td>rosada (pink)</td>\n", "      <td>chamus (yellow)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>D3</td>\n", "      <td>3</td>\n", "      <td>5</td>\n", "      <td>5YR6/14</td>\n", "      <td>#e07800</td>\n", "      <td>orange</td>\n", "      <td>orange</td>\n", "      <td>yellow</td>\n", "      <td>naranja (orange)</td>\n", "      <td>chamus (yellow)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  grid  x  y munsell_code tile_hex english_color spanish_color tsimane_color  \\\n", "0   B1  1  7        5R8/6  #fbb6b0          pink          pink        yellow   \n", "1   D1  1  5       5R6/12  #eb6a68          pink          pink           red   \n", "2   F1  1  3       5R4/14  #bb1933           red           red           red   \n", "3   H1  1  1        5R2/8  #610d25           red           red         brown   \n", "4   A2  2  8       10R9/2  #f2ded8          pink         white         white   \n", "5   C2  2  6      10R7/10  #fb916b        orange        orange        yellow   \n", "6   E2  2  4      10R5/16  #db4300        orange        orange           red   \n", "7   G2  2  2      10R3/10  #872110           red           red         brown   \n", "8   B3  3  7       5YR8/8  #ffb779        orange          pink        yellow   \n", "9   D3  3  5      5YR6/14  #e07800        orange        orange        yellow   \n", "\n", "            spanish             tsimane  \n", "0     rosada (pink)     chamus (yellow)  \n", "1     rosada (pink)        jainas (red)  \n", "2        rojo (red)        jainas (red)  \n", "3        rojo (red)  cafedyeisi (brown)  \n", "4    blanco (white)      jaibas (white)  \n", "5  naranja (orange)     chamus (yellow)  \n", "6  naranja (orange)        jainas (red)  \n", "7        rojo (red)  cafedyeisi (brown)  \n", "8     rosada (pink)     chamus (yellow)  \n", "9  naranja (orange)     chamus (yellow)  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "df = pd.read_csv(\"munsell-array-fixed-choice.csv\")\n", "df.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Project Ideas:\n", "\n", "- For each language, calculate what percentage of chips are named each color. Return dataframes for each language.\n", "\n", "- Create a horizontal bar plot for each language. Each bar represents a color name and the length encodes the percentage of chips that are named that color.\n", "\n", "- Is there a correlation between languages? Create scatter plots. \n", "\t- Hint: the x-axis could be the percentage of chips for English speakers and the y-axis could be the percentage of chips for Tsimane speakers and each data point represents a color name.\n", "\t\n", "\t- You might need to `merge` dataframes.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# For each language, calculate what percentage of chips are named each color. Return dataframes for each language.\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}