{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Volcano Eruptions\n", "\n", "Which volcanoes have experienced the longest eruptions?\n", "\n", "The dataset `volcanic-eruptions.csv` includes the start and end dates for all volcanic eruptions since 1800, along with each volcano's identification number. For eruptions that are still ongoing as of December 2024, the end date is recorded as December 2024.\n", "\n", "This dataset excludes any eruptive pauses shorter than three months. If an eruption resumes after more than three months of inactivity, it is classified as a new eruption.\n", "\n", "Note: Volcano Yasur in Vanuatu is not included in this list due to the absence of a clear start date."]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["# FOR <PERSON>O<PERSON>LE COLAB ONLY.\n", "# Uncomment and run the code below. A dialog will appear to upload files.\n", "# Upload 'volcanic-eruptions.csv' and 'volcano-list.csv'.\n", "\n", "# from google.colab import files\n", "# uploaded = files.upload()"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>volcano_id</th>\n", "      <th>start_date</th>\n", "      <th>end_date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>211020</td>\n", "      <td>07-1913</td>\n", "      <td>04-1944</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>211020</td>\n", "      <td>02-1864</td>\n", "      <td>11-1868</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>211020</td>\n", "      <td>12-1854</td>\n", "      <td>05-1855</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   volcano_id start_date end_date\n", "0      211020    07-1913  04-1944\n", "1      211020    02-1864  11-1868\n", "2      211020    12-1854  05-1855"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "eruptions = pd.read_csv('volcanic-eruptions.csv')\n", "eruptions.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Additional dataset\n", "\n", "The dataset `volcano-list.csv` provides detailed information about each volcano, including its name, country, latitude, longitude, and type."]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>volcano_id</th>\n", "      <th>volcano_name</th>\n", "      <th>country</th>\n", "      <th>volcanic_region_group</th>\n", "      <th>volcanic_region</th>\n", "      <th>volcano_landform</th>\n", "      <th>primary_volcano_type</th>\n", "      <th>activity_evidence</th>\n", "      <th>last_known_eruption</th>\n", "      <th>latitude</th>\n", "      <th>longitude</th>\n", "      <th>elevation_m</th>\n", "      <th>tectonic_setting</th>\n", "      <th>dominant_rock_type</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>210010</td>\n", "      <td>West Eifel Volcanic Field</td>\n", "      <td>Germany</td>\n", "      <td>European Volcanic Regions</td>\n", "      <td>Central European Volcanic Province</td>\n", "      <td>Cluster</td>\n", "      <td>Volcanic field</td>\n", "      <td>Eruption Dated</td>\n", "      <td>8300 BCE</td>\n", "      <td>50.170</td>\n", "      <td>6.850</td>\n", "      <td>600</td>\n", "      <td>Rift zone / Continental crust (&gt;25 km)</td>\n", "      <td>Foidite</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>210020</td>\n", "      <td><PERSON>e des Puys</td>\n", "      <td>France</td>\n", "      <td>European Volcanic Regions</td>\n", "      <td>Western European Volcanic Province</td>\n", "      <td>Cluster</td>\n", "      <td>Lava dome(s)</td>\n", "      <td>Eruption Dated</td>\n", "      <td>4040 BCE</td>\n", "      <td>45.786</td>\n", "      <td>2.981</td>\n", "      <td>1464</td>\n", "      <td>Rift zone / Continental crust (&gt;25 km)</td>\n", "      <td>Basalt / Picro-Basalt</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>210030</td>\n", "      <td>Olot Volcanic Field</td>\n", "      <td>Spain</td>\n", "      <td>European Volcanic Regions</td>\n", "      <td>Western European Volcanic Province</td>\n", "      <td>Cluster</td>\n", "      <td>Volcanic field</td>\n", "      <td>Evidence Credible</td>\n", "      <td>Unknown</td>\n", "      <td>42.170</td>\n", "      <td>2.530</td>\n", "      <td>893</td>\n", "      <td>Intraplate / Continental crust (&gt;25 km)</td>\n", "      <td>Trachybasalt / Tephrite Basanite</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   volcano_id               volcano_name  country      volcanic_region_group  \\\n", "0      210010  West Eifel Volcanic Field  Germany  European Volcanic Regions   \n", "1      210020            Chaine des Puys   France  European Volcanic Regions   \n", "2      210030        Olot Volcanic Field    Spain  European Volcanic Regions   \n", "\n", "                      volcanic_region volcano_landform primary_volcano_type  \\\n", "0  Central European Volcanic Province          Cluster       Volcanic field   \n", "1  Western European Volcanic Province          Cluster         Lava dome(s)   \n", "2  Western European Volcanic Province          Cluster       Volcanic field   \n", "\n", "   activity_evidence last_known_eruption  latitude  longitude  elevation_m  \\\n", "0     Eruption Dated            8300 BCE    50.170      6.850          600   \n", "1     Eruption Dated            4040 BCE    45.786      2.981         1464   \n", "2  Evidence Credible             Unknown    42.170      2.530          893   \n", "\n", "                          tectonic_setting                dominant_rock_type  \n", "0   Rift zone / Continental crust (>25 km)                           Foidite  \n", "1   Rift zone / Continental crust (>25 km)             Basalt / Picro-Basalt  \n", "2  Intraplate / Continental crust (>25 km)  Trachybasalt / Tephrite Basanite  "]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["volcanoes = pd.read_csv('volcano-list.csv')\n", "volcanoes.head(3)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Project Ideas:\n", "\n", "- Find the volcanoes that were erupting as of Dec 2024.\n", "\n", "- Find the volcanoes that have had the longest volcanic eruptions. \n", "\n", "Hints:\n", "- Use `pd.to_datetime`.\n", "\n", "- Merge the volcanoes dataframe into eruptions.\n", "\n", "- Before the merge, reduce the dataframes to the columns of interest.\n", "\n", "- Use `df.sort_values`."]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["# YOUR CODE HERE (add additional cells as needed)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}