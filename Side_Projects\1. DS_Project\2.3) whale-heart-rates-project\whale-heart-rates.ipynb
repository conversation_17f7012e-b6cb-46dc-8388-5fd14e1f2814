{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Blue Whale Heart Rates\n", "\n", "By how much do blue whale heart rates change when they dive down to find food? \n", "\n", "The file `blue-whale-heart-rates.csv` contains heart rate data (measured in beats per minute) for blue whales. The data captures the dynamics of their heart rates as they dive over one hundred meters deep to forage for food. The heart rate measurements are taken approximately every 10 seconds during 8 separate dives.\n", "\n", "### Dive Phases\n", "The data records the heart rates corresponding to different dive states:\n", "1. Descent: The whale begins its dive, descending into the depths.\n", "2. Lunging: The whale opens its mouth and lunges upward, engulfing prey and water.\n", "3. Filtering: After lunging, the whale sinks slightly and filters out water to retain food.\n", "4. Ascent: The whale returns to the surface to breathe, marking the end of a dive.\n", "5.  Surface: The blue whale is now on the surface (upper 5 meters)\n", "\n", "The lunging and filtering process may occur multiple times on a single dive. The next dive cycle begins with another descent."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "timestamp", "rawType": "object", "type": "string"}, {"name": "heart_rate", "rawType": "float64", "type": "float"}, {"name": "dive_id", "rawType": "int64", "type": "integer"}, {"name": "dive_phase", "rawType": "object", "type": "string"}], "ref": "21ae0a4c-bd3c-4130-83c5-fc0735f12239", "rows": [["0", "8/27/18 17:10:10", null, "2", "descent"], ["1", "8/27/18 17:10:22", "5.24", "2", "descent"], ["2", "8/27/18 17:10:31", "6.55", "2", "descent"], ["3", "8/27/18 17:10:40", null, "2", "descent"], ["4", "8/27/18 17:10:48", null, "2", "descent"], ["5", "8/27/18 17:10:56", null, "2", "descent"], ["6", "8/27/18 17:11:06", null, "2", "descent"], ["7", "8/27/18 17:11:17", null, "2", "descent"], ["8", "8/27/18 17:11:20", null, "2", "descent"], ["9", "8/27/18 17:11:23", null, "2", "descent"]], "shape": {"columns": 4, "rows": 10}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>timestamp</th>\n", "      <th>heart_rate</th>\n", "      <th>dive_id</th>\n", "      <th>dive_phase</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>8/27/18 17:10:10</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>8/27/18 17:10:22</td>\n", "      <td>5.24</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8/27/18 17:10:31</td>\n", "      <td>6.55</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8/27/18 17:10:40</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8/27/18 17:10:48</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>8/27/18 17:10:56</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>8/27/18 17:11:06</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8/27/18 17:11:17</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>8/27/18 17:11:20</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>8/27/18 17:11:23</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          timestamp  heart_rate  dive_id dive_phase\n", "0  8/27/18 17:10:10         NaN        2    descent\n", "1  8/27/18 17:10:22        5.24        2    descent\n", "2  8/27/18 17:10:31        6.55        2    descent\n", "3  8/27/18 17:10:40         NaN        2    descent\n", "4  8/27/18 17:10:48         NaN        2    descent\n", "5  8/27/18 17:10:56         NaN        2    descent\n", "6  8/27/18 17:11:06         NaN        2    descent\n", "7  8/27/18 17:11:17         NaN        2    descent\n", "8  8/27/18 17:11:20         NaN        2    descent\n", "9  8/27/18 17:11:23         NaN        2    descent"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "df = pd.read_csv('blue-whale-heart-rates.csv')\n", "df.head(10)\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(23.18061643835616)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df['heart_rate'].mean()\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1087 entries, 0 to 1086\n", "Data columns (total 4 columns):\n", " #   Column      Non-Null Count  Dtype         \n", "---  ------      --------------  -----         \n", " 0   timestamp   1087 non-null   datetime64[ns]\n", " 1   heart_rate  730 non-null    float64       \n", " 2   dive_id     1087 non-null   int64         \n", " 3   dive_phase  1087 non-null   object        \n", "dtypes: datetime64[ns](1), float64(1), int64(1), object(1)\n", "memory usage: 34.1+ KB\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_29276\\2569297244.py:1: UserWarning: Could not infer format, so each element will be parsed individually, falling back to `dateutil`. To ensure parsing is consistent and as-expected, please specify a format.\n", "  df['timestamp'] = pd.to_datetime(df['timestamp'])\n"]}, {"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "timestamp", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "heart_rate", "rawType": "float64", "type": "float"}, {"name": "dive_id", "rawType": "int64", "type": "integer"}, {"name": "dive_phase", "rawType": "object", "type": "string"}], "ref": "90966a15-b375-4e0e-822a-79a810b11847", "rows": [["0", "2018-08-27 17:10:10", null, "2", "descent"], ["1", "2018-08-27 17:10:22", "5.24", "2", "descent"], ["2", "2018-08-27 17:10:31", "6.55", "2", "descent"], ["3", "2018-08-27 17:10:40", null, "2", "descent"], ["4", "2018-08-27 17:10:48", null, "2", "descent"], ["5", "2018-08-27 17:10:56", null, "2", "descent"], ["6", "2018-08-27 17:11:06", null, "2", "descent"], ["7", "2018-08-27 17:11:17", null, "2", "descent"], ["8", "2018-08-27 17:11:20", null, "2", "descent"], ["9", "2018-08-27 17:11:23", null, "2", "descent"], ["10", "2018-08-27 17:11:29", null, "2", "descent"], ["11", "2018-08-27 17:11:45", null, "2", "descent"], ["12", "2018-08-27 17:11:56", null, "2", "descent"], ["13", "2018-08-27 17:11:59", null, "2", "descent"], ["14", "2018-08-27 17:12:11", null, "2", "descent"], ["15", "2018-08-27 17:13:29", null, "2", "lunge"], ["16", "2018-08-27 17:13:43", null, "2", "lunge"], ["17", "2018-08-27 17:13:47", "15.04", "2", "lunge"], ["18", "2018-08-27 17:13:54", "8.76", "2", "lunge"], ["19", "2018-08-27 17:13:57", "17.91", "2", "lunge"], ["20", "2018-08-27 17:14:05", "8.28", "2", "lunge"], ["21", "2018-08-27 17:14:12", "8.26", "2", "lunge"], ["22", "2018-08-27 17:14:19", "8.3", "2", "filter"], ["23", "2018-08-27 17:14:27", "7.42", "2", "filter"], ["24", "2018-08-27 17:14:36", "6.89", "2", "filter"], ["25", "2018-08-27 17:14:45", "6.7", "2", "filter"], ["26", "2018-08-27 17:14:54", "6.43", "2", "filter"], ["27", "2018-08-27 17:15:11", "3.54", "2", "filter"], ["28", "2018-08-27 17:18:23", "5.07", "2", "lunge"], ["29", "2018-08-27 17:18:35", "5.15", "2", "lunge"], ["30", "2018-08-27 17:18:39", "12.5", "2", "lunge"], ["31", "2018-08-27 17:18:47", "8.24", "2", "lunge"], ["32", "2018-08-27 17:18:54", null, "2", "lunge"], ["33", "2018-08-27 17:18:58", null, "2", "lunge"], ["34", "2018-08-27 17:19:02", null, "2", "lunge"], ["35", "2018-08-27 17:19:11", null, "2", "lunge"], ["36", "2018-08-27 17:19:18", "7.98", "2", "filter"], ["37", "2018-08-27 17:19:27", "7.24", "2", "filter"], ["38", "2018-08-27 17:19:35", "7.0", "2", "filter"], ["39", "2018-08-27 17:19:44", "6.86", "2", "filter"], ["40", "2018-08-27 17:19:55", "5.31", "2", "filter"], ["41", "2018-08-27 17:20:09", "4.57", "2", "ascent"], ["42", "2018-08-27 17:20:19", "5.55", "2", "ascent"], ["43", "2018-08-27 17:20:26", "8.46", "2", "ascent"], ["44", "2018-08-27 17:20:29", "20.91", "2", "ascent"], ["45", "2018-08-27 17:20:33", "17.0", "2", "ascent"], ["46", "2018-08-27 17:20:36", "19.42", "2", "ascent"], ["47", "2018-08-27 17:20:39", "17.6", "2", "ascent"], ["48", "2018-08-27 17:20:42", "19.74", "2", "ascent"], ["49", "2018-08-27 17:20:46", "15.11", "2", "ascent"]], "shape": {"columns": 4, "rows": 1087}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>timestamp</th>\n", "      <th>heart_rate</th>\n", "      <th>dive_id</th>\n", "      <th>dive_phase</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2018-08-27 17:10:10</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2018-08-27 17:10:22</td>\n", "      <td>5.24</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2018-08-27 17:10:31</td>\n", "      <td>6.55</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2018-08-27 17:10:40</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2018-08-27 17:10:48</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>descent</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1082</th>\n", "      <td>2018-08-27 19:30:55</td>\n", "      <td>31.41</td>\n", "      <td>13</td>\n", "      <td>surface</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1083</th>\n", "      <td>2018-08-27 19:30:57</td>\n", "      <td>NaN</td>\n", "      <td>13</td>\n", "      <td>surface</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1084</th>\n", "      <td>2018-08-27 19:30:59</td>\n", "      <td>NaN</td>\n", "      <td>13</td>\n", "      <td>surface</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1085</th>\n", "      <td>2018-08-27 19:31:05</td>\n", "      <td>NaN</td>\n", "      <td>13</td>\n", "      <td>surface</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1086</th>\n", "      <td>2018-08-27 19:31:05</td>\n", "      <td>NaN</td>\n", "      <td>13</td>\n", "      <td>surface</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1087 rows × 4 columns</p>\n", "</div>"], "text/plain": ["               timestamp  heart_rate  dive_id dive_phase\n", "0    2018-08-27 17:10:10         NaN        2    descent\n", "1    2018-08-27 17:10:22        5.24        2    descent\n", "2    2018-08-27 17:10:31        6.55        2    descent\n", "3    2018-08-27 17:10:40         NaN        2    descent\n", "4    2018-08-27 17:10:48         NaN        2    descent\n", "...                  ...         ...      ...        ...\n", "1082 2018-08-27 19:30:55       31.41       13    surface\n", "1083 2018-08-27 19:30:57         NaN       13    surface\n", "1084 2018-08-27 19:30:59         NaN       13    surface\n", "1085 2018-08-27 19:31:05         NaN       13    surface\n", "1086 2018-08-27 19:31:05         NaN       13    surface\n", "\n", "[1087 rows x 4 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df['timestamp'] = pd.to_datetime(df['timestamp'])\n", "df.info()\n", "df"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "dive_id", "rawType": "int64", "type": "integer"}, {"name": "timestamp", "rawType": "datetime64[ns]", "type": "datetime"}], "ref": "3b4505e5-96ab-4f0d-8d79-73b1ed2bb8f3", "rows": [["2", "2018-08-27 17:10:10"], ["3", "2018-08-27 17:25:28"], ["4", "2018-08-27 17:40:46"], ["5", "2018-08-27 17:52:09"], ["7", "2018-08-27 18:11:43"]], "shape": {"columns": 1, "rows": 5}}, "text/plain": ["dive_id\n", "2   2018-08-27 17:10:10\n", "3   2018-08-27 17:25:28\n", "4   2018-08-27 17:40:46\n", "5   2018-08-27 17:52:09\n", "7   2018-08-27 18:11:43\n", "Name: timestamp, dtype: datetime64[ns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["earliest_descent = df[df['dive_phase'] == 'descent'].groupby('dive_id')['timestamp'].min()\n", "earliest_descent.head()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "dive_id", "rawType": "int64", "type": "integer"}, {"name": "timestamp", "rawType": "datetime64[ns]", "type": "datetime"}], "ref": "642f5800-8e81-447e-99ce-d6abc9089929", "rows": [["2", "2018-08-27 17:22:36"], ["3", "2018-08-27 17:37:52"], ["4", "2018-08-27 17:49:46"], ["5", "2018-08-27 18:01:46"], ["7", "2018-08-27 18:22:56"]], "shape": {"columns": 1, "rows": 5}}, "text/plain": ["dive_id\n", "2   2018-08-27 17:22:36\n", "3   2018-08-27 17:37:52\n", "4   2018-08-27 17:49:46\n", "5   2018-08-27 18:01:46\n", "7   2018-08-27 18:22:56\n", "Name: timestamp, dtype: datetime64[ns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["Latest_ascent = df[df['dive_phase'] == 'ascent'].groupby('dive_id')['timestamp'].max()\n", "Latest_ascent.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Project Ideas\n", "\n", "#### Warmup\n", "Calculate the average heart rates for each dive phase.\n", "\n", "#### Challenge\n", "Investigate the relationship between dive duration and the maximum heart rate at the surface following the dive.\n", "\n", "1. Data Preparation:\n", "\t- Convert the `timestamp` column to datetime format.\n", "\n", "2. Cal<PERSON> Dive Duration:\n", "\t- For each `dive_id`, identify the earliest timestamp for the descent phase.\n", "\t- For each `dive_id`, find the latest timestamp for the ascent phase.\n", "\t- Compute the `dive_duration` by subtracting the start of the descent from the end of the ascent, then convert this duration to minutes (`dt.total_seconds() / 60`).\n", "\t- Store the results in a new dataframe with 2 columns: `dive_id` and `dive_duration`.\n", "\n", "3. Determine Maximum Surface Heart Rate:\n", "\t- Identify the maximum heart rate recorded at the surface after each dive.\n", "\t- Store the results in a new dataframe with 2 columns: `dive_id` and `max_surface_heart_rate`\n", "\n", "4. <PERSON><PERSON>:\n", "\t- Merge the dive duration and max surface heart rate dataframes.\n", "\t\n", "5.\tVisualize the Data:\n", "\t- Generate a scatter plot with dive duration on the x-axis and maximum surface heart rate on the y-axis to illustrate their relationship."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (<unknown>, line 1)", "output_type": "error", "traceback": ["Traceback \u001b[36m(most recent call last)\u001b[39m:\n", "  File \u001b[92mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\IPython\\core\\interactiveshell.py:3672\u001b[39m in \u001b[95mrun_code\u001b[39m\n    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  Cell \u001b[92mIn[6]\u001b[39m\u001b[92m, line 7\u001b[39m\n    df['dive_duration'] = df.eval(start_descent - end_ascent)\n", "  File \u001b[92mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\frame.py:4954\u001b[39m in \u001b[95meval\u001b[39m\n    return _eval(expr, inplace=inplace, **kwargs)\n", "  File \u001b[92mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\eval.py:339\u001b[39m in \u001b[95meval\u001b[39m\n    parsed_expr = Expr(expr, engine=engine, parser=parser, env=env)\n", "  File \u001b[92mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\expr.py:809\u001b[39m in \u001b[95m__init__\u001b[39m\n    self.terms = self.parse()\n", "  File \u001b[92mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\expr.py:828\u001b[39m in \u001b[95mparse\u001b[39m\n    return self._visitor.visit(self.expr)\n", "  File \u001b[92mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\expr.py:409\u001b[39m in \u001b[95mvisit\u001b[39m\n    raise e\n", "  File \u001b[92mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\computation\\expr.py:405\u001b[39m in \u001b[95mvisit\u001b[39m\n    node = ast.fix_missing_locations(ast.parse(clean))\n", "\u001b[36m  \u001b[39m\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\ast.py:54\u001b[39m\u001b[36m in \u001b[39m\u001b[35mparse\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mreturn compile(source, filename, mode, flags,\u001b[39m\n", "  \u001b[36mFile \u001b[39m\u001b[32m<unknown>:1\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31m-1 days +21 :41 :58\u001b[39m\n       ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m invalid syntax\n"]}], "source": ["start_descent = df[df['dive_phase']== 'descent']['timestamp'].min()\n", "end_ascent = df[df['dive_phase']== 'ascent']['timestamp'].max()\n", "\n", "start_descent\n", "end_ascent\n", "\n", "df['dive_duration'] = df.eval(start_descent - end_ascent)\n", "df.head()\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}