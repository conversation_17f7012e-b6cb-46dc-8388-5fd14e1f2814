/**
Design and implement a program to manage patients in a small hospital.
The system should:

1)Store patient details like ID, name, age, and disease.
2)Register a new patient when they arrive.
3)Display all patients currently in the hospital.
4)Search for a patient by ID.
5)Discharge a patient (remove them from the system).

-Data Structures You Can Use
1)Array (or array of structs) → for storing patient records.
2)Linked List → for maintaining a queue of patients waiting for consultation.

Algorithms You Can Use
1)Searching Algorithm → e.g., Linear Search to find patient by ID.
2)Sorting Algorithm → e.g., Bubble Sort to list patients alphabetically or by age.
**/



#include <stdio.h>
#include <stdlib.h>
#include <string.h>


// Structure for Patient
struct Patient {
    int id;
    char name[50];
    int age;
    char disease[50];
};


// Structure for Queue Node (Linked List)
struct Node {
    int id;
    struct Node* next;
};


// Global Variables
struct Patient patients[100];
int patientCount = 0;

struct Node* front = NULL;
struct Node* rear = NULL;



// Function to register a new patient
void registerPatient() {
    struct Patient p;
    printf("Enter Patient ID: ");
    scanf("%d", &p.id);
    printf("Enter Name: ");
    scanf(" %[^\n]", p.name);
    printf("Enter Age: ");
    scanf("%d", &p.age);
    printf("Enter Disease: ");
    scanf(" %[^\n]", p.disease);

    patients[patientCount++] = p;


    // Add to queue
    struct Node* newNode = (struct Node*)malloc(sizeof(struct Node));
    newNode->id = p.id;
    newNode->next = NULL;
    if (rear == NULL) {
        front = rear = newNode;
    } else {
        rear->next = newNode;
        rear = newNode;
    }

    printf("\n Patient registered successfully!\n");
}



// Function to display all patients
void displayPatients() {
    if (patientCount == 0) {
        printf("\nNo patients in the hospital.\n");
        return;
    }
    printf("\n--- Patient List ---\n");
    for (int i = 0; i < patientCount; i++) {
        printf("ID: %d | Name: %s | Age: %d | Disease: %s\n",
               patients[i].id, patients[i].name, patients[i].age, patients[i].disease);
    }
}



// Function to search for a patient by ID (Linear Search)
void searchPatient() {
    int id;
    printf("Enter Patient ID to search: ");
    scanf("%d", &id);
    for (int i = 0; i < patientCount; i++) {
        if (patients[i].id == id) {
            printf("\nPatient Found:\nID: %d | Name: %s | Age: %d | Disease: %s\n",
                   patients[i].id, patients[i].name, patients[i].age, patients[i].disease);
            return;
        }
    }
    printf("\nPatient not found!\n");
}



// Function to discharge a patient
void dischargePatient() {
    int id;
    printf("Enter Patient ID to discharge: ");
    scanf("%d", &id);

    // Remove from array
    int found = 0;
    for (int i = 0; i < patientCount; i++) {
        if (patients[i].id == id) {
            for (int j = i; j < patientCount - 1; j++) {
                patients[j] = patients[j + 1];
            }
            patientCount--;
            found = 1;
            break;
        }
    }

    // Remove from queue
    struct Node* temp = front;
    struct Node* prev = NULL;
    while (temp != NULL) {
        if (temp->id == id) {
            if (prev == NULL) {
                front = temp->next;
            } else {
                prev->next = temp->next;
            }
            if (temp == rear) {
                rear = prev;
            }
            free(temp);
            break;
        }
        prev = temp;
        temp = temp->next;
    }

    if (found)
        printf("\n Patient discharged successfully!\n");
    else
        printf("\n Patient not found!\n");
}



// Function to sort patients alphabetically by name (Bubble Sort)
void sortPatientsByName() {
    for (int i = 0; i < patientCount - 1; i++) {
        for (int j = 0; j < patientCount - i - 1; j++) {
            if (strcmp(patients[j].name, patients[j + 1].name) > 0) {
                struct Patient temp = patients[j];
                patients[j] = patients[j + 1];
                patients[j + 1] = temp;
            }
        }
    }
    printf("\n Patients sorted by name!\n");
}



// Function to display waiting queue
void displayQueue() {
    if (front == NULL) {
        printf("\nNo patients in queue.\n");
        return;
    }
    printf("\n--- Waiting Queue (by ID) ---\n");
    struct Node* temp = front;
    while (temp != NULL) {
        printf("%d -> ", temp->id);
        temp = temp->next;
    }
    printf("NULL\n");
}



// Main function
int main() {
    int choice;
    do {
        printf("\n==== Hospital Management System ====\n");
        printf("1. Register New Patient\n");
        printf("2. Display All Patients\n");
        printf("3. Search Patient by ID\n");
        printf("4. Discharge Patient\n");
        printf("5. Sort Patients by Name\n");
        printf("6. Display Waiting Queue\n");
        printf("7. Exit\n");
        printf("Enter choice: ");
        scanf("%d", &choice);

        switch (choice) {
            case 1: registerPatient(); break;
            case 2: displayPatients(); break;
            case 3: searchPatient(); break;
            case 4: dischargePatient(); break;
            case 5: sortPatientsByName(); break;
            case 6: displayQueue(); break;
            case 7: printf("\nExiting...\n"); break;
            default: printf("\nInvalid choice! Try again.\n");
        }
    } while (choice != 7);

    return 0;
}
