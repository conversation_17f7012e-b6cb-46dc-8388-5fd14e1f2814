{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3776cb88", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "id": "b3dbfb3d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>eclipse_type</th>\n", "      <th>magnitude</th>\n", "      <th>duration</th>\n", "      <th>region</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>05-18-1901</td>\n", "      <td>Total</td>\n", "      <td>1.068</td>\n", "      <td>06m29s</td>\n", "      <td>s Asia, Australia, e Africa [Total: Indonesia,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>11-11-1901</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0.922</td>\n", "      <td>11m01s</td>\n", "      <td>ne Africa, Asia, w Europe [Annular: ne Africa,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>04-08-1902</td>\n", "      <td>Partial</td>\n", "      <td>0.064</td>\n", "      <td>NaN</td>\n", "      <td>northern Canada</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>05-07-1902</td>\n", "      <td>Partial</td>\n", "      <td>0.859</td>\n", "      <td>NaN</td>\n", "      <td>New Zealand, South Pacific</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10-31-1902</td>\n", "      <td>Partial</td>\n", "      <td>0.696</td>\n", "      <td>NaN</td>\n", "      <td>c Asia, e Europe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>03-29-1903</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0.977</td>\n", "      <td>01m53s</td>\n", "      <td>e Asia, nw N America [Annular: China, Mongolia...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>09-21-1903</td>\n", "      <td>Total</td>\n", "      <td>1.032</td>\n", "      <td>02m12s</td>\n", "      <td>se Africa, Antarctica, s Australia, N.Z. [Tota...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>03-17-1904</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0.937</td>\n", "      <td>08m07s</td>\n", "      <td>e Africa, s Asia[[Annular: Tanzania, Mozambiqu...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>09-09-1904</td>\n", "      <td>Total</td>\n", "      <td>1.071</td>\n", "      <td>06m20s</td>\n", "      <td>Atlantic, nw S America [Total: central Atlanti...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>03-06-1905</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>0.927</td>\n", "      <td>07m58s</td>\n", "      <td>Australia, Antarctica, s Indies [Annular: Aust...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         date eclipse_type  magnitude duration  \\\n", "0  05-18-1901        Total      1.068   06m29s   \n", "1  11-11-1901      <PERSON><PERSON>      0.922   11m01s   \n", "2  04-08-1902      Partial      0.064      NaN   \n", "3  05-07-1902      Partial      0.859      NaN   \n", "4  10-31-1902      Partial      0.696      NaN   \n", "5  03-29-1903      <PERSON><PERSON>      0.977   01m53s   \n", "6  09-21-1903        Total      1.032   02m12s   \n", "7  03-17-1904      <PERSON><PERSON>      0.937   08m07s   \n", "8  09-09-1904        Total      1.071   06m20s   \n", "9  03-06-1905      <PERSON><PERSON>      0.927   07m58s   \n", "\n", "                                              region  \n", "0  s Asia, Australia, e Africa [Total: Indonesia,...  \n", "1  ne Africa, Asia, w Europe [Annular: ne Africa,...  \n", "2                                    northern Canada  \n", "3                         New Zealand, South Pacific  \n", "4                                   c Asia, e Europe  \n", "5  e Asia, nw N America [Annular: China, Mongolia...  \n", "6  se Africa, Antarctica, s Australia, N.Z. [Tota...  \n", "7  e Africa, s Asia[[Annular: Tanzania, Mozambiqu...  \n", "8  Atlantic, nw S America [Total: central Atlanti...  \n", "9  Australia, Antarctica, s Indies [Annular: Aust...  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv('solar-eclipses.csv')\n", "df.head(10)"]}, {"cell_type": "code", "execution_count": 3, "id": "f8b4715f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 444 entries, 0 to 443\n", "Data columns (total 5 columns):\n", " #   Column        Non-Null Count  Dtype  \n", "---  ------        --------------  -----  \n", " 0   date          444 non-null    object \n", " 1   eclipse_type  444 non-null    object \n", " 2   magnitude     444 non-null    float64\n", " 3   duration      289 non-null    object \n", " 4   region        444 non-null    object \n", "dtypes: float64(1), object(4)\n", "memory usage: 17.5+ KB\n"]}], "source": ["df.info()"]}, {"cell_type": "code", "execution_count": 5, "id": "a5ee1cc3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>magnitude</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>444.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.865662</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.247217</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.001000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.824750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.956000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>1.028000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>1.080000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        magnitude\n", "count  444.000000\n", "mean     0.865662\n", "std      0.247217\n", "min      0.001000\n", "25%      0.824750\n", "50%      0.956000\n", "75%      1.028000\n", "max      1.080000"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.describe()"]}, {"cell_type": "code", "execution_count": 15, "id": "d6e5dc36", "metadata": {}, "outputs": [], "source": ["df['date'] = pd.to_datetime(df['date'])\n"]}, {"cell_type": "code", "execution_count": null, "id": "99db97ab", "metadata": {}, "outputs": [{"data": {"text/plain": ["'2100'"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["df['date'].dt.strftime('%Y').max()"]}, {"cell_type": "code", "execution_count": 28, "id": "55990d6d", "metadata": {}, "outputs": [{"data": {"text/plain": ["'1901'"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df['date'].dt.strftime('%Y').min()"]}, {"cell_type": "code", "execution_count": null, "id": "56321acf", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>eclipse_type</th>\n", "      <th>count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Partial</td>\n", "      <td>155</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>141</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Total</td>\n", "      <td>135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Hybrid</td>\n", "      <td>13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  eclipse_type  count\n", "0      Partial    155\n", "1      Annular    141\n", "2        Total    135\n", "3       Hybrid     13"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["Eclipse = df['eclipse_type'].value_counts()\n", "Eclipse = Eclipse.reset_index()\n", "Eclipse"]}, {"cell_type": "code", "execution_count": 46, "id": "c0c3fa05", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "expr must be a string to be evaluated, <class 'bool'> given", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[32m~\\AppData\\Local\\Temp\\ipykernel_25744\\1804214842.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m()\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m df = df.query(\u001b[33m'duration'\u001b[39m != \u001b[33m'NaN'\u001b[39m)\n\u001b[32m      2\u001b[39m df\n", "\u001b[32mc:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\pandas\\core\\frame.py\u001b[39m in \u001b[36m?\u001b[39m\u001b[34m(self, expr, inplace, **kwargs)\u001b[39m\n\u001b[32m   4821\u001b[39m         \"\"\"\n\u001b[32m   4822\u001b[39m         inplace = validate_bool_kwarg(inplace, \u001b[33m\"inplace\"\u001b[39m)\n\u001b[32m   4823\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28;01mnot\u001b[39;00m isinstance(expr, str):\n\u001b[32m   4824\u001b[39m             msg = f\"expr must be a string to be evaluated, {type(expr)} given\"\n\u001b[32m-> \u001b[39m\u001b[32m4825\u001b[39m             \u001b[38;5;28;01mraise\u001b[39;00m ValueError(msg)\n\u001b[32m   4826\u001b[39m         kwargs[\u001b[33m\"level\"\u001b[39m] = kwargs.pop(\u001b[33m\"level\"\u001b[39m, \u001b[32m0\u001b[39m) + \u001b[32m1\u001b[39m\n\u001b[32m   4827\u001b[39m         kwargs[\u001b[33m\"target\"\u001b[39m] = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   4828\u001b[39m         res = self.eval(expr, **kwargs)\n", "\u001b[31mValueError\u001b[39m: expr must be a string to be evaluated, <class 'bool'> given"]}], "source": ["df = df.query('duration' != 'NaN')\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "6cf463d1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}