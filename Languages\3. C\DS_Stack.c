#include <stdio.h>

int top  = -1;
int size = 4;
int stack[4];


void push(int data){
    if(size-1 == top){
        printf("Stack Overflow\n");
        return;
    }
    else{
        top++;
        stack[top] = data;
        printf("Pushed %d into the stack\n",data);
    }
}


void pop(){
    if(top == -1){
        printf("Stack Underflow\n");
        return;
    }
    else{
        printf("Popped %d from the stack\n",stack[top]);
        --top;
    }
}


void peek(){
    printf("The top element is %d\n",stack[top]);
}


void display(){
    for(int i = top; i >= 0; i--){
        printf("%d <- ",stack[i]);
    }
}


int main(){
    
    while(1){

    int Choise, data;
    printf("Pick any option: \n 1)Push \n 2)Pop \n 3)Peek \n 4)Display \n 5)Exit \n");
    scanf("%d",&Choise);

    if(Choise < 1 || <PERSON><PERSON> > 5){
        printf("Invalid <PERSON>\n");
    }
    
    else{
        switch(Choise){
            case 1: {
                printf("Enter any number to push: ");
                scanf("%d",&data);

                push(data);
                break;
            }


            case 2: {
                pop();
                break;
            }


            case 3: {
                peek();
                break;
            }


            case 4: {
                display();
                break;
            }


            case 5: {
                printf("Exiting...");
                return 0;
            }
        }
    }
    }


    return 0;
}