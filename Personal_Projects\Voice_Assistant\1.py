import os
from dotenv import load_dotenv

load_dotenv()  # Executes the function.

AGENT_ID = os.getenv("AGENT_ID")
API_KEY = os.getenv("API_KEY")



from elevenlabs.client import ElevenLabs
    # imports main client object - connects to the ElevenLabs API, manages authentication and API calls.

from elevenlabs.conversational_ai.conversation import Conversation
    # imports a core Conversation class -  represents an ongoing, real-time voice conversation with an ElevenLabs-powered agent,
    # handles the logic for turn-taking, speech-to-text and text-to-speech .

from elevenlabs.conversational_ai.default_audio_interface import DefaultAudioInterface
    # imports a standard implementation - handles the audio input and output during a conversation.

from elevenlabs.types import ConversationConfig 
    # imports a data structure to configure the specific settings for the conversation.
    # (eg- the agent ID, custom voice parameters, or other conversation-specific behaviors.)



user_name = "Dhruv"
schedule = "CCE at 10:00; Workshop at 3:00"
prompt = f"You are a helpful assistant. Your interlocutor has the following schedule: {schedule}."
first_message = f"Hello {user_name}, how can I help you today?"



conversation_override = {
    "agent": {
        "prompt": {
            "prompt": prompt,
        },
        "first_message": first_message,
    },
}

config = ConversationConfig(
    conversation_config_override=conversation_override,
    extra_body={},
    dynamic_variables={},
)

client = ElevenLabs(api_key=API_KEY)

conversation = Conversation(
    client,
    AGENT_ID,
    config=config,
    requires_auth=True,
    audio_interface=DefaultAudioInterface(),
)


