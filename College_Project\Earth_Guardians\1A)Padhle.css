
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}



body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  background-color: #f4f4f4;
  color: #333;
}


h1, h2, h3 {
  color: #2c6e49;
}


a {
  text-decoration: none;
  color: inherit;
}








header {
  background-image: url("5A\ \ Protect.jpg");
  background-size: cover;
  text-align: center;
  border-radius: 10px;
  height: 700px;
}



#Topbar {
  background-color: #d3e8d8;
  height: 90px;
  width: auto;
  border-radius: 3px;
}

.Topbar_elements{
  display: inline-block;
  background-color: #d3e8d8;
  border: none;
  width: auto;
  margin-left: 10px;
  margin-right: 10px;
  font-weight: bolder;
  color: #333;
  font-size: 16px ; 
}



.logo-container {
  position: absolute;
  top: 5px; 
  left: 10px;
  z-index: 1000;
  border-width: 10px;
}

.logo-container img {
  width: 80px; 
  height: auto;
  border-radius: 20px;
  
}






.hero {
  position: relative;
  height: 100vh;
  background: url('5A\ \ Protect.jpg') center/cover no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}


.hero .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.3);
  transition: background 0.7s ease-in;
  z-index: 0;
}

.hero:hover .overlay {
  background: rgba(0, 0, 0, 0.562);
}


.Main_Title {
  position: relative;
  font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif;
  font-size: clamp(40px, 6vw, 80px); /* responsive font size */
  font-weight: bolder;
  color: white;
  z-index: 1; 
  margin: 0;
  padding: 0 15px;
}







#home{ 
  background-color:;
  padding: 40px;
  transition-property: all;
  transition-duration: 0.5s;
  transition-timing-function: ease-in;
}

#home:hover{
  background-color:#d3e8d8;
}







#about{
  background-color:;
  padding: 40px;
  transition-property: all;
  transition-duration: 0.5s;
  transition-timing-function: ease-in;
}

#about:hover{
  background-color:#d3e8d8;
}






.service-item{
  transition-property: all;
  transition-duration: 0.5s;
  transition-timing-function: ease-in;
}

.service-item:hover{
  background-color:#d3e8d8;
}







section {
  padding: 40px 20px;
  text-align: center;
}






.services {
  display: flex;
  justify-content: space-around;
  margin-top: 30px;
}

.service-item {
  background-color: #fff;
  padding: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  width: 30%;
}






.social-icon{
  height: 40px;
  border-radius: 10px;
  margin: 12px;
}








footer {
  text-align: center;
  background-color: #2c6e49;
  color: white;
  padding: 20px 0;
}


