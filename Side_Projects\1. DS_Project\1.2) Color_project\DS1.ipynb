{"cells": [{"cell_type": "markdown", "id": "70036776", "metadata": {}, "source": ["### Data Manipulation with Pandas Library"]}, {"cell_type": "code", "execution_count": 29, "id": "bc8cca41", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n"]}, {"cell_type": "code", "execution_count": 30, "id": "ac36afce", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>grid</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>munsell_code</th>\n", "      <th>tile_hex</th>\n", "      <th>english_color</th>\n", "      <th>spanish_color</th>\n", "      <th>tsimane_color</th>\n", "      <th>spanish</th>\n", "      <th>tsimane</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>B1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>5R8/6</td>\n", "      <td>#fbb6b0</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>yellow</td>\n", "      <td>rosada (pink)</td>\n", "      <td>chamus (yellow)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>5R6/12</td>\n", "      <td>#eb6a68</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>F1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>5R4/14</td>\n", "      <td>#bb1933</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>rojo (red)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>H1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5R2/8</td>\n", "      <td>#610d25</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>brown</td>\n", "      <td>rojo (red)</td>\n", "      <td>cafedyeisi (brown)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A2</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>10R9/2</td>\n", "      <td>#f2ded8</td>\n", "      <td>pink</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>H19</td>\n", "      <td>19</td>\n", "      <td>1</td>\n", "      <td>5RP2/8</td>\n", "      <td>#591340</td>\n", "      <td>purple</td>\n", "      <td>purple</td>\n", "      <td>purple</td>\n", "      <td>morado (purple)</td>\n", "      <td>its<PERSON><PERSON><PERSON> (purple)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>A20</td>\n", "      <td>20</td>\n", "      <td>8</td>\n", "      <td>10RP9/2</td>\n", "      <td>#efdfe0</td>\n", "      <td>pink</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>C20</td>\n", "      <td>20</td>\n", "      <td>6</td>\n", "      <td>10RP7/8</td>\n", "      <td>#ea95a0</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>E20</td>\n", "      <td>20</td>\n", "      <td>4</td>\n", "      <td>10RP5/14</td>\n", "      <td>#d73f6a</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>G20</td>\n", "      <td>20</td>\n", "      <td>2</td>\n", "      <td>10RP3/10</td>\n", "      <td>#891641</td>\n", "      <td>purple</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>rojo (red)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>80 rows × 10 columns</p>\n", "</div>"], "text/plain": ["   grid   x  y munsell_code tile_hex english_color spanish_color  \\\n", "0    B1   1  7        5R8/6  #fbb6b0          pink          pink   \n", "1    D1   1  5       5R6/12  #eb6a68          pink          pink   \n", "2    F1   1  3       5R4/14  #bb1933           red           red   \n", "3    H1   1  1        5R2/8  #610d25           red           red   \n", "4    A2   2  8       10R9/2  #f2ded8          pink         white   \n", "..  ...  .. ..          ...      ...           ...           ...   \n", "75  H19  19  1       5RP2/8  #591340        purple        purple   \n", "76  A20  20  8      10RP9/2  #efdfe0          pink         white   \n", "77  C20  20  6      10RP7/8  #ea95a0          pink          pink   \n", "78  E20  20  4     10RP5/14  #d73f6a          pink          pink   \n", "79  G20  20  2     10RP3/10  #891641        purple           red   \n", "\n", "   tsimane_color          spanish              tsimane  \n", "0         yellow    rosada (pink)      chamus (yellow)  \n", "1            red    rosada (pink)         jainas (red)  \n", "2            red       rojo (red)         jainas (red)  \n", "3          brown       rojo (red)   cafedyeisi (brown)  \n", "4          white   blanco (white)       jaibas (white)  \n", "..           ...              ...                  ...  \n", "75        purple  morado (purple)  <PERSON><PERSON><PERSON><PERSON> (purple)  \n", "76         white   blanco (white)       jaibas (white)  \n", "77           red    rosada (pink)         jainas (red)  \n", "78           red    rosada (pink)         jainas (red)  \n", "79           red       rojo (red)         jainas (red)  \n", "\n", "[80 rows x 10 columns]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["data = pd.read_csv('munsell-array-fixed-choice.csv')\n", "df = data\n", "df"]}, {"cell_type": "code", "execution_count": 31, "id": "75e875bc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(80, 10)\n", "Index(['grid', 'x', 'y', 'munsell_code', 'tile_hex', 'english_color',\n", "       'spanish_color', 'tsimane_color', 'spanish', 'tsimane'],\n", "      dtype='object')\n", "RangeIndex(start=0, stop=80, step=1)\n"]}], "source": ["print(df.shape)\n", "\n", "print(df.columns)\n", "\n", "print(df.index)"]}, {"cell_type": "code", "execution_count": 32, "id": "b55310fe", "metadata": {}, "outputs": [{"data": {"text/plain": ["grid                             H1\n", "x                                 1\n", "y                                 1\n", "munsell_code                  5R2/8\n", "tile_hex                    #610d25\n", "english_color                   red\n", "spanish_color                   red\n", "tsimane_color                 brown\n", "spanish                  rojo (red)\n", "t<PERSON><PERSON><PERSON> (brown)\n", "Name: 3, dtype: object"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["df.il<PERSON>[3]"]}, {"cell_type": "code", "execution_count": 33, "id": "a05e508d", "metadata": {}, "outputs": [{"data": {"text/plain": ["0       pink\n", "1       pink\n", "2        red\n", "3        red\n", "4       pink\n", "       ...  \n", "75    purple\n", "76      pink\n", "77      pink\n", "78      pink\n", "79    purple\n", "Name: english_color, Length: 80, dtype: object"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["df['english_color']"]}, {"cell_type": "code", "execution_count": 34, "id": "36aa8c21", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>grid</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>munsell_code</th>\n", "      <th>tile_hex</th>\n", "      <th>english_color</th>\n", "      <th>spanish_color</th>\n", "      <th>tsimane_color</th>\n", "      <th>spanish</th>\n", "      <th>tsimane</th>\n", "      <th>tp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>B1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>5R8/6</td>\n", "      <td>#fbb6b0</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>yellow</td>\n", "      <td>rosada (pink)</td>\n", "      <td>chamus (yellow)</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>5R6/12</td>\n", "      <td>#eb6a68</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>F1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>5R4/14</td>\n", "      <td>#bb1933</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>rojo (red)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>H1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5R2/8</td>\n", "      <td>#610d25</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>brown</td>\n", "      <td>rojo (red)</td>\n", "      <td>cafedyeisi (brown)</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A2</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>10R9/2</td>\n", "      <td>#f2ded8</td>\n", "      <td>pink</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>H19</td>\n", "      <td>19</td>\n", "      <td>1</td>\n", "      <td>5RP2/8</td>\n", "      <td>#591340</td>\n", "      <td>purple</td>\n", "      <td>purple</td>\n", "      <td>purple</td>\n", "      <td>morado (purple)</td>\n", "      <td>its<PERSON><PERSON><PERSON> (purple)</td>\n", "      <td>19</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>A20</td>\n", "      <td>20</td>\n", "      <td>8</td>\n", "      <td>10RP9/2</td>\n", "      <td>#efdfe0</td>\n", "      <td>pink</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>C20</td>\n", "      <td>20</td>\n", "      <td>6</td>\n", "      <td>10RP7/8</td>\n", "      <td>#ea95a0</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>E20</td>\n", "      <td>20</td>\n", "      <td>4</td>\n", "      <td>10RP5/14</td>\n", "      <td>#d73f6a</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>80</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>G20</td>\n", "      <td>20</td>\n", "      <td>2</td>\n", "      <td>10RP3/10</td>\n", "      <td>#891641</td>\n", "      <td>purple</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>rojo (red)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>40</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>80 rows × 11 columns</p>\n", "</div>"], "text/plain": ["   grid   x  y munsell_code tile_hex english_color spanish_color  \\\n", "0    B1   1  7        5R8/6  #fbb6b0          pink          pink   \n", "1    D1   1  5       5R6/12  #eb6a68          pink          pink   \n", "2    F1   1  3       5R4/14  #bb1933           red           red   \n", "3    H1   1  1        5R2/8  #610d25           red           red   \n", "4    A2   2  8       10R9/2  #f2ded8          pink         white   \n", "..  ...  .. ..          ...      ...           ...           ...   \n", "75  H19  19  1       5RP2/8  #591340        purple        purple   \n", "76  A20  20  8      10RP9/2  #efdfe0          pink         white   \n", "77  C20  20  6      10RP7/8  #ea95a0          pink          pink   \n", "78  E20  20  4     10RP5/14  #d73f6a          pink          pink   \n", "79  G20  20  2     10RP3/10  #891641        purple           red   \n", "\n", "   tsimane_color          spanish              tsimane   tp  \n", "0         yellow    rosada (pink)      chamus (yellow)    7  \n", "1            red    rosada (pink)         jainas (red)    5  \n", "2            red       rojo (red)         jainas (red)    3  \n", "3          brown       rojo (red)   cafedyeisi (brown)    1  \n", "4          white   blanco (white)       jaibas (white)   16  \n", "..           ...              ...                  ...  ...  \n", "75        purple  morado (purple)  <PERSON><PERSON><PERSON><PERSON> (purple)   19  \n", "76         white   blanco (white)       jaibas (white)  160  \n", "77           red    rosada (pink)         jainas (red)  120  \n", "78           red    rosada (pink)         jainas (red)   80  \n", "79           red       rojo (red)         jainas (red)   40  \n", "\n", "[80 rows x 11 columns]"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["df['tp'] = df['x'] * df['y']\n", "df"]}, {"cell_type": "code", "execution_count": 35, "id": "c51ba659", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3800\n", "47.5\n", "160\n", "1\n"]}], "source": ["print(df['tp'].sum())\n", "print(df['tp'].mean())\n", "print(df['tp'].max())\n", "print(df['tp'].min())"]}, {"cell_type": "code", "execution_count": 36, "id": "bc4c3a0e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>grid</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>munsell_code</th>\n", "      <th>tile_hex</th>\n", "      <th>english_color</th>\n", "      <th>spanish_color</th>\n", "      <th>tsimane_color</th>\n", "      <th>spanish</th>\n", "      <th>tsimane</th>\n", "      <th>tp</th>\n", "      <th>tp2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>B1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>5R8/6</td>\n", "      <td>#fbb6b0</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>yellow</td>\n", "      <td>rosada (pink)</td>\n", "      <td>chamus (yellow)</td>\n", "      <td>7</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>5R6/12</td>\n", "      <td>#eb6a68</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>F1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>5R4/14</td>\n", "      <td>#bb1933</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>rojo (red)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>H1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5R2/8</td>\n", "      <td>#610d25</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>brown</td>\n", "      <td>rojo (red)</td>\n", "      <td>cafedyeisi (brown)</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A2</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>10R9/2</td>\n", "      <td>#f2ded8</td>\n", "      <td>pink</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>16</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>H19</td>\n", "      <td>19</td>\n", "      <td>1</td>\n", "      <td>5RP2/8</td>\n", "      <td>#591340</td>\n", "      <td>purple</td>\n", "      <td>purple</td>\n", "      <td>purple</td>\n", "      <td>morado (purple)</td>\n", "      <td>its<PERSON><PERSON><PERSON> (purple)</td>\n", "      <td>19</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>A20</td>\n", "      <td>20</td>\n", "      <td>8</td>\n", "      <td>10RP9/2</td>\n", "      <td>#efdfe0</td>\n", "      <td>pink</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>160</td>\n", "      <td>28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>C20</td>\n", "      <td>20</td>\n", "      <td>6</td>\n", "      <td>10RP7/8</td>\n", "      <td>#ea95a0</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>120</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>E20</td>\n", "      <td>20</td>\n", "      <td>4</td>\n", "      <td>10RP5/14</td>\n", "      <td>#d73f6a</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>80</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>G20</td>\n", "      <td>20</td>\n", "      <td>2</td>\n", "      <td>10RP3/10</td>\n", "      <td>#891641</td>\n", "      <td>purple</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>rojo (red)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>40</td>\n", "      <td>22</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>80 rows × 12 columns</p>\n", "</div>"], "text/plain": ["   grid   x  y munsell_code tile_hex english_color spanish_color  \\\n", "0    B1   1  7        5R8/6  #fbb6b0          pink          pink   \n", "1    D1   1  5       5R6/12  #eb6a68          pink          pink   \n", "2    F1   1  3       5R4/14  #bb1933           red           red   \n", "3    H1   1  1        5R2/8  #610d25           red           red   \n", "4    A2   2  8       10R9/2  #f2ded8          pink         white   \n", "..  ...  .. ..          ...      ...           ...           ...   \n", "75  H19  19  1       5RP2/8  #591340        purple        purple   \n", "76  A20  20  8      10RP9/2  #efdfe0          pink         white   \n", "77  C20  20  6      10RP7/8  #ea95a0          pink          pink   \n", "78  E20  20  4     10RP5/14  #d73f6a          pink          pink   \n", "79  G20  20  2     10RP3/10  #891641        purple           red   \n", "\n", "   tsimane_color          spanish              tsimane   tp  tp2  \n", "0         yellow    rosada (pink)      chamus (yellow)    7    8  \n", "1            red    rosada (pink)         jainas (red)    5    6  \n", "2            red       rojo (red)         jainas (red)    3    4  \n", "3          brown       rojo (red)   cafedyeisi (brown)    1    2  \n", "4          white   blanco (white)       jaibas (white)   16   10  \n", "..           ...              ...                  ...  ...  ...  \n", "75        purple  morado (purple)  <PERSON><PERSON><PERSON><PERSON> (purple)   19   20  \n", "76         white   blanco (white)       jaibas (white)  160   28  \n", "77           red    rosada (pink)         jainas (red)  120   26  \n", "78           red    rosada (pink)         jainas (red)   80   24  \n", "79           red       rojo (red)         jainas (red)   40   22  \n", "\n", "[80 rows x 12 columns]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["df['tp2'] = df.eval('x + y')\n", "df"]}, {"cell_type": "code", "execution_count": 37, "id": "89055357", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>grid</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>munsell_code</th>\n", "      <th>tile_hex</th>\n", "      <th>english_color</th>\n", "      <th>spanish_color</th>\n", "      <th>tsimane_color</th>\n", "      <th>spanish</th>\n", "      <th>tsimane</th>\n", "      <th>tp2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>B1</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>5R8/6</td>\n", "      <td>#fbb6b0</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>yellow</td>\n", "      <td>rosada (pink)</td>\n", "      <td>chamus (yellow)</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>D1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>5R6/12</td>\n", "      <td>#eb6a68</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>F1</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>5R4/14</td>\n", "      <td>#bb1933</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>rojo (red)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>H1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5R2/8</td>\n", "      <td>#610d25</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>brown</td>\n", "      <td>rojo (red)</td>\n", "      <td>cafedyeisi (brown)</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A2</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>10R9/2</td>\n", "      <td>#f2ded8</td>\n", "      <td>pink</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75</th>\n", "      <td>H19</td>\n", "      <td>19</td>\n", "      <td>1</td>\n", "      <td>5RP2/8</td>\n", "      <td>#591340</td>\n", "      <td>purple</td>\n", "      <td>purple</td>\n", "      <td>purple</td>\n", "      <td>morado (purple)</td>\n", "      <td>its<PERSON><PERSON><PERSON> (purple)</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>A20</td>\n", "      <td>20</td>\n", "      <td>8</td>\n", "      <td>10RP9/2</td>\n", "      <td>#efdfe0</td>\n", "      <td>pink</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>77</th>\n", "      <td>C20</td>\n", "      <td>20</td>\n", "      <td>6</td>\n", "      <td>10RP7/8</td>\n", "      <td>#ea95a0</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>E20</td>\n", "      <td>20</td>\n", "      <td>4</td>\n", "      <td>10RP5/14</td>\n", "      <td>#d73f6a</td>\n", "      <td>pink</td>\n", "      <td>pink</td>\n", "      <td>red</td>\n", "      <td>rosada (pink)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>G20</td>\n", "      <td>20</td>\n", "      <td>2</td>\n", "      <td>10RP3/10</td>\n", "      <td>#891641</td>\n", "      <td>purple</td>\n", "      <td>red</td>\n", "      <td>red</td>\n", "      <td>rojo (red)</td>\n", "      <td>jain<PERSON> (red)</td>\n", "      <td>22</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>80 rows × 11 columns</p>\n", "</div>"], "text/plain": ["   grid   x  y munsell_code tile_hex english_color spanish_color  \\\n", "0    B1   1  7        5R8/6  #fbb6b0          pink          pink   \n", "1    D1   1  5       5R6/12  #eb6a68          pink          pink   \n", "2    F1   1  3       5R4/14  #bb1933           red           red   \n", "3    H1   1  1        5R2/8  #610d25           red           red   \n", "4    A2   2  8       10R9/2  #f2ded8          pink         white   \n", "..  ...  .. ..          ...      ...           ...           ...   \n", "75  H19  19  1       5RP2/8  #591340        purple        purple   \n", "76  A20  20  8      10RP9/2  #efdfe0          pink         white   \n", "77  C20  20  6      10RP7/8  #ea95a0          pink          pink   \n", "78  E20  20  4     10RP5/14  #d73f6a          pink          pink   \n", "79  G20  20  2     10RP3/10  #891641        purple           red   \n", "\n", "   tsimane_color          spanish              tsimane  tp2  \n", "0         yellow    rosada (pink)      chamus (yellow)    8  \n", "1            red    rosada (pink)         jainas (red)    6  \n", "2            red       rojo (red)         jainas (red)    4  \n", "3          brown       rojo (red)   cafedyeisi (brown)    2  \n", "4          white   blanco (white)       jaibas (white)   10  \n", "..           ...              ...                  ...  ...  \n", "75        purple  morado (purple)  <PERSON><PERSON><PERSON><PERSON> (purple)   20  \n", "76         white   blanco (white)       jaibas (white)   28  \n", "77           red    rosada (pink)         jainas (red)   26  \n", "78           red    rosada (pink)         jainas (red)   24  \n", "79           red       rojo (red)         jainas (red)   22  \n", "\n", "[80 rows x 11 columns]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["df.drop(columns = 'tp' ,inplace=True)\n", "df"]}, {"cell_type": "code", "execution_count": 38, "id": "4da81515", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>grid</th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>munsell_code</th>\n", "      <th>tile_hex</th>\n", "      <th>english_color</th>\n", "      <th>spanish_color</th>\n", "      <th>tsimane_color</th>\n", "      <th>spanish</th>\n", "      <th>tsimane</th>\n", "      <th>tp2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>A2</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>10R9/2</td>\n", "      <td>#f2ded8</td>\n", "      <td>pink</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>A4</td>\n", "      <td>4</td>\n", "      <td>8</td>\n", "      <td>10YR9/4</td>\n", "      <td>#fedeae</td>\n", "      <td>yellow</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>A6</td>\n", "      <td>6</td>\n", "      <td>8</td>\n", "      <td>10Y9/6</td>\n", "      <td>#ebe78a</td>\n", "      <td>yellow</td>\n", "      <td>yellow</td>\n", "      <td>yellow</td>\n", "      <td>am<PERSON><PERSON> (yellow)</td>\n", "      <td>chamus (yellow)</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>A8</td>\n", "      <td>8</td>\n", "      <td>8</td>\n", "      <td>10GY9/4</td>\n", "      <td>#c5eebd</td>\n", "      <td>green</td>\n", "      <td>green</td>\n", "      <td>green</td>\n", "      <td>verde (green)</td>\n", "      <td>shand<PERSON> (green)</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36</th>\n", "      <td>A10</td>\n", "      <td>10</td>\n", "      <td>8</td>\n", "      <td>10G9/2</td>\n", "      <td>#c9eadd</td>\n", "      <td>green</td>\n", "      <td>green</td>\n", "      <td>white</td>\n", "      <td>verde (green)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44</th>\n", "      <td>A12</td>\n", "      <td>12</td>\n", "      <td>8</td>\n", "      <td>10BG9/2</td>\n", "      <td>#cae8e8</td>\n", "      <td>blue</td>\n", "      <td>lightblue</td>\n", "      <td>white</td>\n", "      <td>celeste (light blue)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>A14</td>\n", "      <td>14</td>\n", "      <td>8</td>\n", "      <td>10B9/2</td>\n", "      <td>#d5e5ed</td>\n", "      <td>blue</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60</th>\n", "      <td>A16</td>\n", "      <td>16</td>\n", "      <td>8</td>\n", "      <td>10PB9/2</td>\n", "      <td>#e2e1ec</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>A18</td>\n", "      <td>18</td>\n", "      <td>8</td>\n", "      <td>10P9/2</td>\n", "      <td>#eadfe6</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>A20</td>\n", "      <td>20</td>\n", "      <td>8</td>\n", "      <td>10RP9/2</td>\n", "      <td>#efdfe0</td>\n", "      <td>pink</td>\n", "      <td>white</td>\n", "      <td>white</td>\n", "      <td>blanco (white)</td>\n", "      <td>j<PERSON><PERSON> (white)</td>\n", "      <td>28</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   grid   x  y munsell_code tile_hex english_color spanish_color  \\\n", "4    A2   2  8       10R9/2  #f2ded8          pink         white   \n", "12   A4   4  8      10YR9/4  #fedeae        yellow         white   \n", "20   A6   6  8       10Y9/6  #ebe78a        yellow        yellow   \n", "28   A8   8  8      10GY9/4  #c5eebd         green         green   \n", "36  A10  10  8       10G9/2  #c9eadd         green         green   \n", "44  A12  12  8      10BG9/2  #cae8e8          blue     lightblue   \n", "52  A14  14  8       10B9/2  #d5e5ed          blue         white   \n", "60  A16  16  8      10PB9/2  #e2e1ec         white         white   \n", "68  A18  18  8       10P9/2  #eadfe6         white         white   \n", "76  A20  20  8      10RP9/2  #efdfe0          pink         white   \n", "\n", "   tsimane_color               spanish           tsimane  tp2  \n", "4          white        blanco (white)    jaibas (white)   10  \n", "12         white        blanco (white)    jaibas (white)   12  \n", "20        yellow     amarillo (yellow)   chamus (yellow)   14  \n", "28         green         verde (green)  shand<PERSON> (green)   16  \n", "36         white         verde (green)    jaibas (white)   18  \n", "44         white  celeste (light blue)    jaibas (white)   20  \n", "52         white        blanco (white)    jaibas (white)   22  \n", "60         white        blanco (white)    jaibas (white)   24  \n", "68         white        blanco (white)    jaibas (white)   26  \n", "76         white        blanco (white)    jaibas (white)   28  "]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["df.query('y > 7')"]}, {"cell_type": "code", "execution_count": 39, "id": "9934b73d", "metadata": {}, "outputs": [{"data": {"text/plain": ["english_color\n", "black      5\n", "blue      12\n", "brown      3\n", "green      6\n", "orange     2\n", "pink       1\n", "purple    16\n", "red        1\n", "white     16\n", "yellow     4\n", "Name: x, dtype: int64"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby('english_color')['x'].min()"]}, {"cell_type": "code", "execution_count": 40, "id": "6394e675", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>english_color</th>\n", "      <th>x</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>black</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>blue</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>brown</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>green</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>orange</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>pink</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>purple</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>red</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>white</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>yellow</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  english_color   x\n", "0         black   5\n", "1          blue  12\n", "2         brown   3\n", "3         green   6\n", "4        orange   2\n", "5          pink   1\n", "6        purple  16\n", "7           red   1\n", "8         white  16\n", "9        yellow   4"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["df.groupby('english_color')['x'].min().reset_index()"]}, {"cell_type": "code", "execution_count": null, "id": "1629a33a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "3116bef7", "metadata": {}, "source": ["### Data Visualization with matplotlib library"]}, {"cell_type": "code", "execution_count": 41, "id": "ac1b8d11", "metadata": {}, "outputs": [{"data": {"text/plain": ["<BarContainer object of 80 artists>"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.bar(df['x'],df['y'])"]}, {"cell_type": "code", "execution_count": 47, "id": "d68baa3c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x1d077727890>]"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(df['x'],df['y'])"]}, {"cell_type": "code", "execution_count": 43, "id": "d0594905", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.collections.PathCollection at 0x1d075599810>"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(df['x'],df['y'])"]}, {"cell_type": "code", "execution_count": 44, "id": "6d3980a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<BarContainer object of 80 artists>"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.barh(df['x'],df['y'])"]}, {"cell_type": "code", "execution_count": 46, "id": "857614c8", "metadata": {}, "outputs": [{"data": {"text/plain": ["(array([8., 8., 8., 8., 8., 8., 8., 8., 8., 8.]),\n", " array([ 1. ,  2.9,  4.8,  6.7,  8.6, 10.5, 12.4, 14.3, 16.2, 18.1, 20. ]),\n", " <BarContainer object of 10 artists>)"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.hist(df['x'])"]}, {"cell_type": "code", "execution_count": 50, "id": "1c2ad262", "metadata": {}, "outputs": [{"data": {"text/plain": ["Text(0, 0.5, 'Y-axis')"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.scatter(df['x'],df['y'])\n", "plt.title('Title')           \n", "plt.xlabel('X-axis')         \n", "plt.ylabel('Y-axis')"]}, {"cell_type": "code", "execution_count": null, "id": "7a286aef", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}