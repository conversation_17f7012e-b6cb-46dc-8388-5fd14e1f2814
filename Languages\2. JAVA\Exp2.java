import java.util.Scanner;

public class Exp2 {

    static class Student {
    String name;
    String address;
    int age;

    Student() {
        name = "unknown";
        age = 0;
        address = "not available";
    }

    public void setInfo(String name, int age) {
        this.name = name;
        this.age = age;
    }

    public void setInfo(String name, int age, String address) {
        this.name = name;
        this.age = age;
        this.address = address;
    }
    }



    public static void main(String[] args) {
        Scanner input = new Scanner(System.in);

        Student[] studentList = new Student[10];

        for (int idx = 0; idx < studentList.length; idx++) {
            System.out.println("Student " + (idx + 1) + ":");

            System.out.print("Enter Name: ");
            String stuName = input.nextLine();

            System.out.print("Enter Age: ");
            int stuAge = input.nextInt();
            input.nextLine(); // consume leftover newline

            System.out.print("Enter Address (Press ENTER to skip): ");
            String stuAddress = input.nextLine();

            studentList[idx] = new Student();
            if (stuAddress.isEmpty()) {
                studentList[idx].setInfo(stuName, stuAge);
            } else {
                studentList[idx].setInfo(stuName, stuAge, stuAddress);
            }
        }

        System.out.println("\n--- Student Details ---");
        int count = 1;
        for (Student s : studentList) {
            System.out.println("Student " + (count++) + ":");
            System.out.println("Name: " + s.name);
            System.out.println("Age: " + s.age);
            System.out.println("Address: " + s.address + "\n");
        }
        input.close();
    }

}