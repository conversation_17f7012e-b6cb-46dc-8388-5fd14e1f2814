{"cells": [{"cell_type": "markdown", "id": "8f89f527", "metadata": {}, "source": ["## Machine Learning Process\n", "\n", " 1. Loading Data \n", " 2. Data Preprocessing\n", " 3. Model Training\n", " 4. Model Evaluation\n", " 5. Making Predictions"]}, {"cell_type": "code", "execution_count": 1, "id": "634e11e7", "metadata": {}, "outputs": [], "source": ["# Importing Libraries\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.linear_model import LogisticRegression"]}, {"cell_type": "code", "execution_count": 2, "id": "c62ef038", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sepal_length</th>\n", "      <th>sepal_width</th>\n", "      <th>petal_length</th>\n", "      <th>petal_width</th>\n", "      <th>species</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5.1</td>\n", "      <td>3.5</td>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "      <td>setosa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.9</td>\n", "      <td>3.0</td>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "      <td>setosa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.7</td>\n", "      <td>3.2</td>\n", "      <td>1.3</td>\n", "      <td>0.2</td>\n", "      <td>setosa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.6</td>\n", "      <td>3.1</td>\n", "      <td>1.5</td>\n", "      <td>0.2</td>\n", "      <td>setosa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.0</td>\n", "      <td>3.6</td>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "      <td>setosa</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145</th>\n", "      <td>6.7</td>\n", "      <td>3.0</td>\n", "      <td>5.2</td>\n", "      <td>2.3</td>\n", "      <td>virginica</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>6.3</td>\n", "      <td>2.5</td>\n", "      <td>5.0</td>\n", "      <td>1.9</td>\n", "      <td>virginica</td>\n", "    </tr>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>6.5</td>\n", "      <td>3.0</td>\n", "      <td>5.2</td>\n", "      <td>2.0</td>\n", "      <td>virginica</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148</th>\n", "      <td>6.2</td>\n", "      <td>3.4</td>\n", "      <td>5.4</td>\n", "      <td>2.3</td>\n", "      <td>virginica</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>5.9</td>\n", "      <td>3.0</td>\n", "      <td>5.1</td>\n", "      <td>1.8</td>\n", "      <td>virginica</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>150 rows × 5 columns</p>\n", "</div>"], "text/plain": ["     sepal_length  sepal_width  petal_length  petal_width    species\n", "0             5.1          3.5           1.4          0.2     setosa\n", "1             4.9          3.0           1.4          0.2     setosa\n", "2             4.7          3.2           1.3          0.2     setosa\n", "3             4.6          3.1           1.5          0.2     setosa\n", "4             5.0          3.6           1.4          0.2     setosa\n", "..            ...          ...           ...          ...        ...\n", "145           6.7          3.0           5.2          2.3  virginica\n", "146           6.3          2.5           5.0          1.9  virginica\n", "147           6.5          3.0           5.2          2.0  virginica\n", "148           6.2          3.4           5.4          2.3  virginica\n", "149           5.9          3.0           5.1          1.8  virginica\n", "\n", "[150 rows x 5 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Loading dataset and dispaying first few rows\n", "iris_data = pd.read_csv('https://raw.githubusercontent.com/mwaskom/seaborn-data/master/iris.csv')\n", "iris_data"]}, {"cell_type": "code", "execution_count": 3, "id": "aa59f76c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sepal_length</th>\n", "      <th>sepal_width</th>\n", "      <th>petal_length</th>\n", "      <th>petal_width</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5.1</td>\n", "      <td>3.5</td>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4.9</td>\n", "      <td>3.0</td>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>4.7</td>\n", "      <td>3.2</td>\n", "      <td>1.3</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4.6</td>\n", "      <td>3.1</td>\n", "      <td>1.5</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5.0</td>\n", "      <td>3.6</td>\n", "      <td>1.4</td>\n", "      <td>0.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145</th>\n", "      <td>6.7</td>\n", "      <td>3.0</td>\n", "      <td>5.2</td>\n", "      <td>2.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>6.3</td>\n", "      <td>2.5</td>\n", "      <td>5.0</td>\n", "      <td>1.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>6.5</td>\n", "      <td>3.0</td>\n", "      <td>5.2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148</th>\n", "      <td>6.2</td>\n", "      <td>3.4</td>\n", "      <td>5.4</td>\n", "      <td>2.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>5.9</td>\n", "      <td>3.0</td>\n", "      <td>5.1</td>\n", "      <td>1.8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>150 rows × 4 columns</p>\n", "</div>"], "text/plain": ["     sepal_length  sepal_width  petal_length  petal_width\n", "0             5.1          3.5           1.4          0.2\n", "1             4.9          3.0           1.4          0.2\n", "2             4.7          3.2           1.3          0.2\n", "3             4.6          3.1           1.5          0.2\n", "4             5.0          3.6           1.4          0.2\n", "..            ...          ...           ...          ...\n", "145           6.7          3.0           5.2          2.3\n", "146           6.3          2.5           5.0          1.9\n", "147           6.5          3.0           5.2          2.0\n", "148           6.2          3.4           5.4          2.3\n", "149           5.9          3.0           5.1          1.8\n", "\n", "[150 rows x 4 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Split the data set into features int x and label y\n", "x = iris_data.drop(columns = ['species'])\n", "y = iris_data['species']\n", "x"]}, {"cell_type": "code", "execution_count": 4, "id": "f30481ec", "metadata": {}, "outputs": [], "source": ["# Create ML model\n", "model = LogisticRegression()"]}, {"cell_type": "code", "execution_count": 5, "id": "fd525e57", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sklearn\\linear_model\\_logistic.py:473: ConvergenceWarning: lbfgs failed to converge after 100 iteration(s) (status=1):\n", "STOP: TOTAL NO. OF ITERATIONS REACHED LIMIT\n", "\n", "Increase the number of iterations to improve the convergence (max_iter=100).\n", "You might also want to scale the data as shown in:\n", "    https://scikit-learn.org/stable/modules/preprocessing.html\n", "Please also refer to the documentation for alternative solver options:\n", "    https://scikit-learn.org/stable/modules/linear_model.html#logistic-regression\n", "  n_iter_i = _check_optimize_result(\n"]}, {"data": {"text/html": ["<style>#sk-container-id-1 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: #000;\n", "  --sklearn-color-text-muted: #666;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-1 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-1 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-1 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-1 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-1 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-1 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-1 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-1 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-1 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: flex;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "  align-items: start;\n", "  justify-content: space-between;\n", "  gap: 0.5em;\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label .caption {\n", "  font-size: 0.6rem;\n", "  font-weight: lighter;\n", "  color: var(--sklearn-color-text-muted);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-1 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-1 div.sk-toggleable__content {\n", "  display: none;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  display: block;\n", "  width: 100%;\n", "  overflow: visible;\n", "}\n", "\n", "#sk-container-id-1 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-1 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-1 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-1 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-1 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-1 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-1 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-1 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-1 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-1 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-1 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 0.5em;\n", "  text-align: center;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-1 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-1 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-1 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".estimator-table summary {\n", "    padding: .5rem;\n", "    font-family: monospace;\n", "    cursor: pointer;\n", "}\n", "\n", ".estimator-table details[open] {\n", "    padding-left: 0.1rem;\n", "    padding-right: 0.1rem;\n", "    padding-bottom: 0.3rem;\n", "}\n", "\n", ".estimator-table .parameters-table {\n", "    margin-left: auto !important;\n", "    margin-right: auto !important;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(odd) {\n", "    background-color: #fff;\n", "}\n", "\n", ".estimator-table .parameters-table tr:nth-child(even) {\n", "    background-color: #f6f6f6;\n", "}\n", "\n", ".estimator-table .parameters-table tr:hover {\n", "    background-color: #e0e0e0;\n", "}\n", "\n", ".estimator-table table td {\n", "    border: 1px solid rgba(106, 105, 104, 0.232);\n", "}\n", "\n", ".user-set td {\n", "    color:rgb(255, 94, 0);\n", "    text-align: left;\n", "}\n", "\n", ".user-set td.value pre {\n", "    color:rgb(255, 94, 0) !important;\n", "    background-color: transparent !important;\n", "}\n", "\n", ".default td {\n", "    color: black;\n", "    text-align: left;\n", "}\n", "\n", ".user-set td i,\n", ".default td i {\n", "    color: black;\n", "}\n", "\n", ".copy-paste-icon {\n", "    background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA0NDggNTEyIj48IS0tIUZvbnQgQXdlc29tZSBGcmVlIDYuNy4yIGJ5IEBmb250YXdlc29tZSAtIGh0dHBzOi8vZm9udGF3ZXNvbWUuY29tIExpY2Vuc2UgLSBodHRwczovL2ZvbnRhd2Vzb21lLmNvbS9saWNlbnNlL2ZyZWUgQ29weXJpZ2h0IDIwMjUgRm9udGljb25zLCBJbmMuLS0+PHBhdGggZD0iTTIwOCAwTDMzMi4xIDBjMTIuNyAwIDI0LjkgNS4xIDMzLjkgMTQuMWw2Ny45IDY3LjljOSA5IDE0LjEgMjEuMiAxNC4xIDMzLjlMNDQ4IDMzNmMwIDI2LjUtMjEuNSA0OC00OCA0OGwtMTkyIDBjLTI2LjUgMC00OC0yMS41LTQ4LTQ4bDAtMjg4YzAtMjYuNSAyMS41LTQ4IDQ4LTQ4ek00OCAxMjhsODAgMCAwIDY0LTY0IDAgMCAyNTYgMTkyIDAgMC0zMiA2NCAwIDAgNDhjMCAyNi41LTIxLjUgNDgtNDggNDhMNDggNTEyYy0yNi41IDAtNDgtMjEuNS00OC00OEwwIDE3NmMwLTI2LjUgMjEuNS00OCA0OC00OHoiLz48L3N2Zz4=);\n", "    background-repeat: no-repeat;\n", "    background-size: 14px 14px;\n", "    background-position: 0;\n", "    display: inline-block;\n", "    width: 14px;\n", "    height: 14px;\n", "    cursor: pointer;\n", "}\n", "</style><body><div id=\"sk-container-id-1\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>LogisticRegression()</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-1\" type=\"checkbox\" checked><label for=\"sk-estimator-id-1\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow\"><div><div>LogisticRegression</div></div><div><a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.7/modules/generated/sklearn.linear_model.LogisticRegression.html\">?<span>Documentation for LogisticRegression</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></div></label><div class=\"sk-toggleable__content fitted\" data-param-prefix=\"\">\n", "        <div class=\"estimator-table\">\n", "            <details>\n", "                <summary>Parameters</summary>\n", "                <table class=\"parameters-table\">\n", "                  <tbody>\n", "                    \n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('penalty',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">penalty&nbsp;</td>\n", "            <td class=\"value\">&#x27;l2&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('dual',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">dual&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('tol',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">tol&nbsp;</td>\n", "            <td class=\"value\">0.0001</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('C',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">C&nbsp;</td>\n", "            <td class=\"value\">1.0</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('fit_intercept',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">fit_intercept&nbsp;</td>\n", "            <td class=\"value\">True</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('intercept_scaling',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">intercept_scaling&nbsp;</td>\n", "            <td class=\"value\">1</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('class_weight',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">class_weight&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('random_state',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">random_state&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('solver',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">solver&nbsp;</td>\n", "            <td class=\"value\">&#x27;lbfgs&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('max_iter',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">max_iter&nbsp;</td>\n", "            <td class=\"value\">100</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('multi_class',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">multi_class&nbsp;</td>\n", "            <td class=\"value\">&#x27;deprecated&#x27;</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('verbose',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">verbose&nbsp;</td>\n", "            <td class=\"value\">0</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('warm_start',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">warm_start&nbsp;</td>\n", "            <td class=\"value\">False</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('n_jobs',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">n_jobs&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "\n", "        <tr class=\"default\">\n", "            <td><i class=\"copy-paste-icon\"\n", "                 onclick=\"copyToClipboard('l1_ratio',\n", "                          this.parentElement.nextElementSibling)\"\n", "            ></i></td>\n", "            <td class=\"param\">l1_ratio&nbsp;</td>\n", "            <td class=\"value\">None</td>\n", "        </tr>\n", "    \n", "                  </tbody>\n", "                </table>\n", "            </details>\n", "        </div>\n", "    </div></div></div></div></div><script>function copyToClipboard(text, element) {\n", "    // Get the parameter prefix from the closest toggleable content\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${text}` : text;\n", "\n", "    const originalStyle = element.style;\n", "    const computedStyle = window.getComputedStyle(element);\n", "    const originalWidth = computedStyle.width;\n", "    const originalHTML = element.innerHTML.replace('Copied!', '');\n", "\n", "    navigator.clipboard.writeText(fullParamName)\n", "        .then(() => {\n", "            element.style.width = originalWidth;\n", "            element.style.color = 'green';\n", "            element.innerHTML = \"Copied!\";\n", "\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        })\n", "        .catch(err => {\n", "            console.error('Failed to copy:', err);\n", "            element.style.color = 'red';\n", "            element.innerHTML = \"Failed!\";\n", "            setTimeout(() => {\n", "                element.innerHTML = originalHTML;\n", "                element.style = originalStyle;\n", "            }, 2000);\n", "        });\n", "    return false;\n", "}\n", "\n", "document.querySelectorAll('.fa-regular.fa-copy').forEach(function(element) {\n", "    const toggleableContent = element.closest('.sk-toggleable__content');\n", "    const paramPrefix = toggleableContent ? toggleableContent.dataset.paramPrefix : '';\n", "    const paramName = element.parentElement.nextElementSibling.textContent.trim();\n", "    const fullParamName = paramPrefix ? `${paramPrefix}${paramName}` : paramName;\n", "\n", "    element.setAttribute('title', fullParamName);\n", "});\n", "</script></body>"], "text/plain": ["LogisticRegression()"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Train the model\n", "model.fit(x.values,y) \n", "\n", "# (This fit method is called to train the logistic regression model on features x and their corresponding labels y. \\\n", "# This step involves learning the relationship between the features and the target variable. So let's run this code.)"]}, {"cell_type": "code", "execution_count": 6, "id": "7a4152ec", "metadata": {}, "outputs": [{"data": {"text/plain": ["array(['versicolor'], dtype=object)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Prediction using labelled model\n", "predictions = model.predict([[5.6,3.5,4.5,0.2]])\n", "predictions"]}, {"cell_type": "code", "execution_count": 7, "id": "3e2838fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['versicolor']\n"]}], "source": ["print(predictions)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}